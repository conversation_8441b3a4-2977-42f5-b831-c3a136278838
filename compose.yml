name: aiki
services:
  aiki-api:
    container_name: "aiki-api"
    image: ghcr.io/claudius<PERSON><PERSON>/aiki-api:latest
    restart: unless-stopped
    security_opt:
      - no-new-privileges
    ports:
      - 127.0.0.1:$API_PORT:$API_PORT
    environment:
      NODE_ENV: $NODE_ENV
      API_PORT: $API_PORT
      API_TOKEN: $API_TOKEN
      TZ: $TZ
      API_URL: $API_URL
      PUID: $PUID
      PGID: $PGID
      # Database
      DB_URL: $DB_URL
      REDIS_HOST: $REDIS_HOST
      REDIS_PORT: $REDIS_PORT
      # NOTE: All TTLs are in seconds
      # JWT
      JWT_SECRET: $JWT_SECRET
      JWT_ISSUER: $JWT_ISSUER
      JWT_AUDIENCE: $JWT_AUDIENCE
      JWT_ACCESS_TOKEN_TTL: $JWT_ACCESS_TOKEN_TTL
      JWT_REFRESH_TOKEN_TTL: $JWT_REFRESH_TOKEN_TTL
      # THROTTLER
      THROTTLER_TTL: $THROTTLER_TTL
      THROTTLER_LIMIT: $THROTTLER_LIMIT
      # HTTP (axios)
      HTTP_TIMEOUT: $HTTP_TIMEOUT
      HTTP_MAX_REDIRECTS: $HTTP_MAX_REDIRECTS
      # Email
      EMAIL_FROM: $EMAIL_FROM
      EMAIL_SENDER: $EMAIL_SENDER
      EMAIL_HOST: $EMAIL_HOST
      EMAIL_PORT: $EMAIL_PORT
      EMAIL_USERNAME: $EMAIL_USERNAME
      EMAIL_PASSWORD: $EMAIL_PASSWORD
      EMAIL_SECURE: $EMAIL_SECURE
      VERIFICATION_CODE_TTL: $VERIFICATION_CODE_TTL

      # Paystack
      PAYSTACK_SECRET_KEY: $PAYSTACK_SECRET_KEY
      PAYSTACK_PUBLIC_KEY: $PAYSTACK_PUBLIC_KEY
      PAYSTACK_BASE_URL: $PAYSTACK_BASE_URL
      PAYSTACK_CALLBACK_URL: $PAYSTACK_CALLBACK_URL
      PAYSTACK_WEBHOOK_URL: $PAYSTACK_WEBHOOK_URL
      FLOW_PLAN_CODE: $FLOW_PLAN_CODE
    networks:
      - pg
      - redis
      - default
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:$API_PORT/health"]
      interval: 10s
      timeout: 5s
      retries: 5

networks:
  pg:
    external: true
  redis:
    external: true
