This file is a merged representation of the entire codebase, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
.github/
  workflows/
    api.yml
scripts/
  manage-secrets.sh
src/
  core/
    common/
      decorators/
        is-boolean.decorator.ts
      dto/
        id.dto.ts
        registry-dates.dto.ts
        remove.dto.ts
      filters/
        database-exception.filter.ts
        global-exceptions.filter.ts
        not-found-exception.filter.ts
      interceptors/
        logging.interceptor.ts
        response.interceptor.ts
      interfaces/
        database-error.interface.ts
        payload.interface.ts
        pkg.interface.ts
        request-with-cookies.interface.ts
        response.interface.ts
      utils/
        array.util.ts
        common.constants.ts
        compare-ids.util.ts
        http-error.util.ts
        id.util.ts
        regex.util.ts
    config/
      app.config.ts
      cookie.config.ts
      data-source.ts
      db.config.ts
      email.config.ts
      helmet.config.ts
      http.config.ts
      jwt.config.ts
      payment.config.ts
      providers.config.ts
      redis.config.ts
      throttler.config.ts
      tokens.config.ts
    db/
      migrations/
        1759837262107-starter-models.ts
        1759838873727-add-email-verification.ts
        1759900768299-create-tasks-table.ts
        1759904513924-create-payments-and-update-plans.ts
        1759999999999-add-database-constraints-and-indexes.ts
      db.module.ts
    redis/
      redis.module.ts
      redis.service.ts
    swagger/
      jwt-cookie-header.ts
  modules/
    auth/
      decorators/
        active-user.decorator.ts
        public.decorator.ts
        roles.decorator.ts
      dto/
        auth.dto.ts
        change-password.dto.ts
        refresh-token.dto.ts
        resend-verification.dto.ts
        verify-email.dto.ts
      guards/
        jwt-auth.guard.ts
        local-auth.guard.ts
        role.guard.ts
        session.guard.ts
      strategies/
        jwt.strategy.ts
        local.strategy.ts
      auth.controller.ts
      auth.module.ts
      auth.service.ts
    email/
      interfaces/
        email-options.ts
      templates/
        verification.hbs
      email.module.ts
      email.service.ts
    health/
      health.controller.ts
      health.module.ts
      health.service.ts
    payments/
      dto/
        create-payment.dto.ts
        initialize-payment.dto.ts
        update-payment.dto.ts
        verify-payment.dto.ts
      entities/
        payment.entity.ts
      enums/
        payment-status.enum.ts
        payment-type.enum.ts
      interfaces/
        initialize-response.interface.ts
        subscription-response.interface.ts
        verify-response.interface.ts
      payments.controller.ts
      payments.module.ts
      payments.service.ts
    plans/
      dto/
        create-plan.dto.ts
        update-plan.dto.ts
      entities/
        plan.entity.ts
      plans.controller.ts
      plans.module.ts
      plans.service.ts
    tasks/
      dto/
        create-task.dto.ts
        update-task.dto.ts
      entities/
        task.entity.ts
      enums/
        task-status.enum.ts
      tasks.controller.ts
      tasks.module.ts
      tasks.service.ts
    users/
      dto/
        create-user.dto.ts
        update-user.dto.ts
      entities/
        user.entity.ts
      enums/
        roles.enum.ts
      interfaces/
        user.interface.ts
      users.controller.ts
      users.module.ts
      users.service.ts
  app.module.ts
  main.ts
test/
  app.e2e-spec.ts
  jest-e2e.json
.dockerignore
.env.example
.gitignore
.prettierrc
compose.dev.yml
compose.yml
Dockerfile
Dockerfile.dev
eslint.config.mjs
nest-cli.json
package.json
README.md
tsconfig.build.json
tsconfig.json
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="scripts/manage-secrets.sh">
#!/usr/bin/env bash

set -euo pipefail

# ==== CONFIG ====
ENV_FILE=".env"
REPO=$(gh repo view --json nameWithOwner -q .nameWithOwner 2>/dev/null || echo "unknown/repo")

# ==== COLORS ====
bold=$(tput bold)
normal=$(tput sgr0)
green=$(tput setaf 2)
red=$(tput setaf 1)
yellow=$(tput setaf 3)
blue=$(tput setaf 4)

# ==== UTILS ====
function log() {
  echo "${green}✔${normal} $*"
}

function warn() {
  echo "${yellow}⚠${normal} $*"
}

function err() {
  echo "${red}✘${normal} $*"
}

function title() {
  echo -e "\n${bold}${blue}🔐 $*${normal}\n"
}

function help() {
  cat <<EOF
${bold}GitHub Secrets Manager${normal}

Usage:
  ./manage-secrets.sh [command]

Commands:
  ${bold}add${normal}           Add secrets from ${ENV_FILE} to ${REPO}
  ${bold}list${normal}          List all secrets in the repository
  ${bold}delete <key...>${normal}  Delete specific secret(s)
  ${bold}delete --all${normal}  Delete all secrets
  ${bold}sync${normal}          Delete all secrets, then add from ${ENV_FILE}
  ${bold}help${normal}          Show this help message

Examples:
  ./manage-secrets.sh add
  ./manage-secrets.sh list
  ./manage-secrets.sh delete DB_USER DB_PASS
  ./manage-secrets.sh delete --all
  ./manage-secrets.sh sync
EOF
}

# ==== CORE ====
function add_secrets() {
  if [[ ! -f "$ENV_FILE" ]]; then
    err "ENV file '$ENV_FILE' not found."
    exit 1
  fi

  title "Adding secrets from $ENV_FILE to $REPO"

while IFS='=' read -r key value; do
  [[ -z "$key" || "$key" =~ ^# || ! "$key" =~ ^[A-Z0-9_]+$ ]] && continue

  # Handle multiline quoted values
  if [[ "$value" =~ ^\" ]]; then
    value="${value#\"}"  # remove opening quote
    while ! [[ "$value" =~ \"$ ]]; do
      read -r next_line || break
      value+=$'\n'"$next_line"
    done
    value="${value%\"}"  # remove closing quote
  fi

  log "Adding $key"
  gh secret set "$key" --repo "$REPO" --body "$value"
done < "$ENV_FILE"

}

function list_secrets() {
  title "Listing secrets in $REPO"
  gh secret list --repo "$REPO"
}

function delete_secret() {
  local key=$1
  log "Deleting $key"
  gh secret delete "$key" --repo "$REPO" || warn "Failed to delete $key"
}

function delete_secrets() {
  shift
  for key in "$@"; do
    delete_secret "$key"
  done
}

function delete_all_secrets() {
  title "Deleting all secrets from $REPO"
  mapfile -t secrets < <(gh secret list --repo "$REPO" --json name -q '.[].name')
  if [[ ${#secrets[@]} -eq 0 ]]; then
    warn "No secrets found."
    return
  fi
  for secret in "${secrets[@]}"; do
    delete_secret "$secret"
  done
}

function sync_secrets() {
  delete_all_secrets
  add_secrets
}

# ==== ENTRYPOINT ====
case "${1:-}" in
  add)
    add_secrets
    ;;
  list)
    list_secrets
    ;;
  delete)
    shift
    if [[ "${1:-}" == "--all" ]]; then
      delete_all_secrets
    else
      delete_secrets "$@"
    fi
    ;;
  sync)
    sync_secrets
    ;;
  help|-h|--help)
    help
    ;;
  *)
    err "Invalid or missing command."
    help
    exit 1
    ;;
esac
</file>

<file path="src/core/common/decorators/is-boolean.decorator.ts">
import { applyDecorators } from '@nestjs/common';
import { Transform } from 'class-transformer';
import {
  IsBoolean as DefaultIsBoolean,
  ValidationOptions,
} from 'class-validator';

const toBoolean = (value: unknown) => {
  switch (value) {
    case null:
      return 'Failure';

    case 'true':
      return true;
    case 'false':
      return false;

    default:
      return value;
  }
};

const ToBoolean = () =>
  Transform(({ obj, key }: { obj: Record<string, unknown>; key: string }) =>
    toBoolean(obj[key]),
  );

/** Checks if the value is a boolean. Works with query params. */
export const IsBoolean = (
  validationOptions?: ValidationOptions,
): PropertyDecorator =>
  applyDecorators(DefaultIsBoolean(validationOptions), ToBoolean());
</file>

<file path="src/core/common/dto/id.dto.ts">
import { IsUUID } from 'class-validator';

export class IdDto {
  /**
   * Entity ID
   * @example "372cf3e1-abda-4678-a79d-ed254b1f3fcb"
   */
  @IsUUID()
  id: string;
}
</file>

<file path="src/core/common/dto/registry-dates.dto.ts">
import { CreateDateColumn, DeleteDateColumn, UpdateDateColumn } from 'typeorm';

export class RegistryDates {
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at' })
  deletedAt: Date;
}
</file>

<file path="src/core/common/dto/remove.dto.ts">
import { IsOptional } from 'class-validator';
import { IsBoolean } from '../decorators/is-boolean.decorator';

export class RemoveDto {
  @IsOptional()
  @IsBoolean()
  readonly soft?: boolean;
}
</file>

<file path="src/core/common/filters/database-exception.filter.ts">
import { ArgumentsHost, Catch } from '@nestjs/common';
import { BaseExceptionFilter } from '@nestjs/core';
import { Response } from 'express';
import { QueryFailedError } from 'typeorm';

import { DatabaseError } from '../interfaces/database-error.interface';
import { HttpError } from '../utils/http-error.util';
import { extractFromText } from '../utils/regex.util';

@Catch(QueryFailedError)
export class DatabaseExceptionFilter extends BaseExceptionFilter {
  catch(exception: DatabaseError, host: ArgumentsHost) {
    const response = host.switchToHttp().getResponse<Response>();

    const { code, detail, table } = exception;
    const { httpError, description } = this.createErrorData(code, detail);

    if (!httpError) {
      return super.catch(exception, host);
    }

    const { status, error } = httpError;
    const { fieldName, fieldValue } = this.extractMessageData(detail);
    const meta = { description, fieldName, fieldValue, table };

    response.status(status).json({
      statusCode: status,
      message: detail,
      error,
      meta,
    });
  }

  private extractMessageData(message: string) {
    const fieldName = extractFromText(message, this.FIELD_NAME_REGEX);
    const fieldValue = extractFromText(message, this.FIELD_VALUE_REGEX);
    return { fieldName, fieldValue };
  }

  private readonly FIELD_NAME_REGEX = /(?<=Key \()\w+/;
  private readonly FIELD_VALUE_REGEX = /(?<=\)=\().*?(?=\))/;

  private createErrorData(code: string, message: string) {
    let httpError: HttpError | undefined = undefined;
    let description: string | undefined = undefined;

    switch (code) {
      case this.DatabaseErrorCode.ASSOCIATION_NOT_FOUND_OR_NOT_NULL_VIOLATION:
        switch (true) {
          case message.includes(this.MessageSnippet.ASSOCIATION_NOT_FOUND):
            httpError = HttpError.NOT_FOUND;
            description = this.Description.ASSOCIATION_NOT_FOUND;
            break;

          case message.includes(this.MessageSnippet.NOT_NULL_VIOLATION):
            httpError = HttpError.CONFLICT;
            description = this.Description.NOT_NULL_VIOLATION;
            break;
        }
        break;

      case this.DatabaseErrorCode.UNIQUE_VIOLATION:
        httpError = HttpError.CONFLICT;
        description = this.Description.UNIQUE_VIOLATION;
        break;
    }

    return { httpError, description };
  }

  private readonly DatabaseErrorCode = {
    ASSOCIATION_NOT_FOUND_OR_NOT_NULL_VIOLATION: '23503',
    UNIQUE_VIOLATION: '23505',
  } as const satisfies Record<string, string>;

  private readonly MessageSnippet = {
    ASSOCIATION_NOT_FOUND: 'is not present',
    NOT_NULL_VIOLATION: 'is still referenced',
  } as const satisfies Record<string, string>;

  private readonly Description = {
    ASSOCIATION_NOT_FOUND: 'Associated entity not found',
    NOT_NULL_VIOLATION: 'Cannot delete due to NOT NULL constraint',
    UNIQUE_VIOLATION: 'Unique constraint violation',
  } as const satisfies Record<string, string>;
}
</file>

<file path="src/core/common/filters/global-exceptions.filter.ts">
import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Response } from 'express';

@Catch()
export class GlobalExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionsFilter.name);

  constructor(private readonly config: ConfigService) {}

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const exceptionResponse = exception.getResponse() as
        | string
        | { message?: string };
      message =
        typeof exceptionResponse === 'string'
          ? exceptionResponse
          : exceptionResponse &&
              typeof exceptionResponse === 'object' &&
              'message' in exceptionResponse
            ? (exceptionResponse.message ?? exception.message)
            : exception.message;
    }

    this.logger.error(
      `HTTP Status: ${status} Error Message: ${message}`,
      exception instanceof Error ? exception.stack : 'Unknown error',
    );

    response.status(status).json({
      code: status,
      timestamp: new Date().toISOString(),
      status: Number(status) >= 400 && Number(status) < 500 ? 'fail' : 'error',
      path: request.url,
      method: request.method,
      message,
      ...(this.config.get<string>('NODE_ENV') === 'development' && {
        stack: exception instanceof Error ? exception.stack : undefined,
      }),
    });
  }
}
</file>

<file path="src/core/common/filters/not-found-exception.filter.ts">
import { ArgumentsHost, Catch, ExceptionFilter } from '@nestjs/common';
import { Response } from 'express';
import { EntityNotFoundError } from 'typeorm';

import { HttpError } from '../utils/http-error.util';
import { extractFromText } from '../utils/regex.util';

@Catch(EntityNotFoundError)
export class NotFoundExceptionFilter implements ExceptionFilter {
  catch(exception: EntityNotFoundError, host: ArgumentsHost) {
    const response = host.switchToHttp().getResponse<Response>();

    const { status, error } = HttpError.NOT_FOUND;
    const { entityName } = this.extractMessageData(exception.message);
    const message = `${entityName} not found`;

    response.status(status).json({
      statusCode: status,
      message,
      error,
    });
  }

  private extractMessageData(message: string) {
    const entityName = extractFromText(message, this.ENTITY_NAME_REGEX);
    return { entityName };
  }

  private readonly ENTITY_NAME_REGEX = /(?<=type\s")\w+/;
}
</file>

<file path="src/core/common/interceptors/logging.interceptor.ts">
import {
  CallHandler,
  ExecutionContext,
  Injectable,
  Logger,
  NestInterceptor,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

@Injectable()
export class Traffic implements NestInterceptor {
  private readonly logger = new Logger(Traffic.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();
    const { method, url } = request;
    const startTime = Date.now();

    return next.handle().pipe(
      tap(() => {
        const endTime = Date.now();
        const timeTaken = endTime - startTime;
        const statusCode = response.statusCode;
        const icon = statusCode < 400 ? '🛈' : '❌';

        this.logger.log(
          `${icon} [${statusCode}] ${method} ${url} ${timeTaken}ms`,
        );
      }),
    );
  }
}
</file>

<file path="src/core/common/interceptors/response.interceptor.ts">
import {
  CallHandler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Response } from 'express';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ApiResponse } from '../interfaces/response.interface';

@Injectable()
export class ResponseInterceptor<T>
  implements NestInterceptor<T, ApiResponse<T>>
{
  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<ApiResponse<T>> {
    const response = context.switchToHttp().getResponse<Response>();
    const code = response.statusCode;

    return next.handle().pipe(
      map((data: T) => {
        if (Array.isArray(data)) {
          return {
            code,
            status: 'success',
            results: data.length,
            data,
          };
        }

        return {
          code,
          status: 'success',
          data,
        };
      }),
    );
  }
}
</file>

<file path="src/core/common/interfaces/database-error.interface.ts">
export interface DatabaseError {
  readonly code: string;
  readonly detail: string;
  readonly table: string;
}
</file>

<file path="src/core/common/interfaces/payload.interface.ts">
import { UserRole } from 'src/modules/users/enums/roles.enum';

export interface IPayload {
  /**
   * Subject of the token (User ID)
   * @example 123e4567-e89b-12d3-a456-************
   */
  readonly sub: string;

  /**
   * User Email
   * @example <EMAIL>
   */
  readonly email: string;

  /**
   * User Role
   * @example Reader
   */
  readonly role: UserRole;
}
</file>

<file path="src/core/common/interfaces/pkg.interface.ts">
export interface IPackageJson {
  name?: string;
  description?: string;
  version?: string;
  author?: string;
  // license?: string;
  // [key: string]: unknown;
}
</file>

<file path="src/core/common/interfaces/request-with-cookies.interface.ts">
export interface RequestWithCookies extends Request {
  cookies: Record<string, string>;
}
</file>

<file path="src/core/common/interfaces/response.interface.ts">
export interface ApiResponse<T = unknown> {
  status: string;
  data?: T;
  results?: number;
}
</file>

<file path="src/core/common/utils/array.util.ts">
export type NonEmptyArray<T> = [T, ...T[]];
</file>

<file path="src/core/common/utils/common.constants.ts">
import { ValidationPipeOptions } from '@nestjs/common';

export const VALIDATION_PIPE_OPTIONS = {
  whitelist: true,
  forbidNonWhitelisted: true,
  transform: true,
  transformOptions: {
    enableImplicitConversion: true,
  },
} as const satisfies ValidationPipeOptions;
</file>

<file path="src/core/common/utils/compare-ids.util.ts">
import { ForbiddenException } from '@nestjs/common';

export const compareIds = (userId: string, requiredId: string) => {
  if (userId !== requiredId)
    throw new ForbiddenException('You are not allowed to perform this action!');
};
</file>

<file path="src/core/common/utils/http-error.util.ts">
import { HttpStatus } from '@nestjs/common';

export const HttpError = {
  NOT_FOUND: {
    status: HttpStatus.NOT_FOUND,
    error: 'Not Found',
  },
  CONFLICT: {
    status: HttpStatus.CONFLICT,
    error: 'Conflict',
  },
  PAYLOAD_TOO_LARGE: {
    status: HttpStatus.PAYLOAD_TOO_LARGE,
    error: 'Payload Too Large',
  },
  UNSUPPORTED_MEDIA_TYPE: {
    status: HttpStatus.UNSUPPORTED_MEDIA_TYPE,
    error: 'Unsupported Media Type',
  },
  BAD_REQUEST: {
    status: HttpStatus.BAD_REQUEST,
    error: 'Bad Request',
  },
} as const satisfies Record<string, IHttpError>;

interface IHttpError {
  readonly status: HttpStatus;
  readonly error: string;
}

export type HttpError = (typeof HttpError)[keyof typeof HttpError];
</file>

<file path="src/core/common/utils/id.util.ts">
import { ArrayUniqueIdentifier } from 'class-validator';

export function wrapId(idOrIds: string | string[]) {
  if (Array.isArray(idOrIds)) {
    const ids = idOrIds;
    return ids.map((id) => ({ id }));
  }

  const id = idOrIds;
  return { id };
}

export const IdentifierFn = {
  ID_DTO: (id: string) => id,
} as const satisfies Record<string, ArrayUniqueIdentifier>;
</file>

<file path="src/core/common/utils/regex.util.ts">
export const extractFromText = (text: string, regex: RegExp) => {
  const match = text.match(regex);
  return match ? match[0] : undefined;
};
</file>

<file path="src/core/config/cookie.config.ts">
import { ApiConfig } from './app.config';

export default {
  access: {
    secure: true,
    httpOnly: true,
    sameSite: true,
    maxAge: ApiConfig.JWT_ACCESS_TOKEN_TTL * 1000, // cookies maxAge is in ms
  },
  refresh: {
    secure: true,
    httpOnly: true,
    sameSite: true,
    maxAge: ApiConfig.JWT_REFRESH_TOKEN_TTL * 1000, // cookies maxAge is in ms
  },
};
</file>

<file path="src/core/config/db.config.ts">
import { registerAs } from '@nestjs/config';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { ApiConfig } from './app.config';

export default registerAs('db', () => {
  const config = {
    type: 'postgres',
    url: ApiConfig.DB_URL,
    autoLoadEntities: true,
  } as const satisfies TypeOrmModuleOptions;
  return config;
});
</file>

<file path="src/core/config/helmet.config.ts">
export default {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", 'data:', 'https:'],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true,
  },
};
</file>

<file path="src/core/config/http.config.ts">
import { ApiConfig } from './app.config';

export default {
  timeout: ApiConfig.HTTP_TIMEOUT,
  maxRedirects: ApiConfig.HTTP_MAX_REDIRECTS,
};
</file>

<file path="src/core/config/jwt.config.ts">
import { registerAs } from '@nestjs/config';
import { JwtModuleOptions } from '@nestjs/jwt';
import { ApiConfig } from './app.config';

export default registerAs('jwt', () => {
  const config = {
    secret: ApiConfig.JWT_SECRET,
    signOptions: {
      expiresIn: ApiConfig.JWT_ACCESS_TOKEN_TTL,
    },
    refreshTokenTtl: ApiConfig.JWT_REFRESH_TOKEN_TTL,
  } as const satisfies JwtModuleOptions & {
    refreshTokenTtl: string | number;
  };
  return config;
});
</file>

<file path="src/core/config/payment.config.ts">
import { registerAs } from '@nestjs/config';

import { ApiConfig } from './app.config';

export default registerAs('payment', () => ({
  secretKey: ApiConfig.PAYSTACK_SECRET_KEY,
  publicKey: ApiConfig.PAYSTACK_PUBLIC_KEY,
  baseUrl: ApiConfig.PAYSTACK_BASE_URL,
  callbackUrl: ApiConfig.PAYSTACK_CALLBACK_URL,
  webhookUrl: ApiConfig.PAYSTACK_WEBHOOK_URL,
  flowPlanCode: ApiConfig.FLOW_PLAN_CODE,
}));
</file>

<file path="src/core/config/redis.config.ts">
import { ApiConfig } from './app.config';

export default {
  host: ApiConfig.REDIS_HOST || 'redis',
  port: ApiConfig.REDIS_PORT || 6379,
};

export const REDIS_CLIENT = 'REDIS_CLIENT';
</file>

<file path="src/core/config/throttler.config.ts">
import { registerAs } from '@nestjs/config';
import { seconds, ThrottlerModuleOptions } from '@nestjs/throttler';

import { ApiConfig } from './app.config';

export default registerAs('throttler', () => {
  const config = [
    {
      ttl: seconds(ApiConfig.THROTTLER_TTL),
      limit: ApiConfig.THROTTLER_LIMIT,
    },
  ] as const satisfies ThrottlerModuleOptions;
  return config;
});
</file>

<file path="src/core/config/tokens.config.ts">
export default {
  access: 'AikiAccessToken',
  refresh: 'AikiRefreshToken',
};
</file>

<file path="src/core/db/migrations/1759837262107-starter-models.ts">
import { MigrationInterface, QueryRunner } from "typeorm";

export class StarterModels1759837262107 implements MigrationInterface {
    name = 'StarterModels1759837262107'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "plans" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "slug" character varying(255) NOT NULL,
                "name" character varying(255),
                "description" text,
                "task_limit" integer,
                "price" numeric(10, 2) NOT NULL DEFAULT '0',
                "is_subscription" boolean NOT NULL DEFAULT false,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "deleted_at" TIMESTAMP,
                CONSTRAINT "UQ_e7b71bb444e74ee067df057397e" UNIQUE ("slug"),
                CONSTRAINT "PK_3720521a81c7c24fe9b7202ba61" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."user_role" AS ENUM('admin', 'user')
        `);
        await queryRunner.query(`
            CREATE TABLE "users" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "email" character varying(255) NOT NULL,
                "password" character varying(255) NOT NULL,
                "name" character varying(255),
                "phone" character varying(20),
                "bio" character varying(255),
                "verified" boolean NOT NULL DEFAULT false,
                "tasks_left" integer NOT NULL DEFAULT '5',
                "renews_at" TIMESTAMP,
                "role" "public"."user_role" NOT NULL DEFAULT 'user',
                "last_login_at" TIMESTAMP,
                "plan_id" uuid,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "deleted_at" TIMESTAMP,
                CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3" UNIQUE ("email"),
                CONSTRAINT "UQ_a000cca60bcf04454e727699490" UNIQUE ("phone"),
                CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            ALTER TABLE "users"
            ADD CONSTRAINT "FK_bc1cd381147462a1c604b425f7a" FOREIGN KEY ("plan_id") REFERENCES "plans"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "users" DROP CONSTRAINT "FK_bc1cd381147462a1c604b425f7a"
        `);
        await queryRunner.query(`
            DROP TABLE "users"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."user_role"
        `);
        await queryRunner.query(`
            DROP TABLE "plans"
        `);
    }

}
</file>

<file path="src/core/db/migrations/1759838873727-add-email-verification.ts">
import { MigrationInterface, QueryRunner } from "typeorm";

export class AddEmailVerification1759838873727 implements MigrationInterface {
    name = 'AddEmailVerification1759838873727'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "users"
            ADD "verification_code" character varying(6)
        `);
        await queryRunner.query(`
            ALTER TABLE "users"
            ADD "verification_code_expires_at" TIMESTAMP
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "users" DROP COLUMN "verification_code_expires_at"
        `);
        await queryRunner.query(`
            ALTER TABLE "users" DROP COLUMN "verification_code"
        `);
    }

}
</file>

<file path="src/core/db/migrations/1759900768299-create-tasks-table.ts">
import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTasksTable1759900768299 implements MigrationInterface {
    name = 'CreateTasksTable1759900768299'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TYPE "public"."task_status" AS ENUM('todo', 'in_progress', 'done')
        `);
        await queryRunner.query(`
            CREATE TABLE "tasks" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "title" character varying(255) NOT NULL,
                "description" text,
                "status" "public"."task_status" NOT NULL DEFAULT 'todo',
                "due_at" TIMESTAMP,
                "user_id" uuid NOT NULL,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "deleted_at" TIMESTAMP,
                CONSTRAINT "PK_8d12ff38fcc62aaba2cab748772" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            ALTER TABLE "tasks"
            ADD CONSTRAINT "FK_db55af84c226af9dce09487b61b" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "tasks" DROP CONSTRAINT "FK_db55af84c226af9dce09487b61b"
        `);
        await queryRunner.query(`
            DROP TABLE "tasks"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."task_status"
        `);
    }

}
</file>

<file path="src/core/db/migrations/1759904513924-create-payments-and-update-plans.ts">
import { MigrationInterface, QueryRunner } from "typeorm";

export class CreatePaymentsAndUpdatePlans1759904513924 implements MigrationInterface {
    name = 'CreatePaymentsAndUpdatePlans1759904513924'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TYPE "public"."payment_status" AS ENUM('pending', 'success', 'failed')
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."payment_type" AS ENUM('one_time', 'subscription')
        `);
        await queryRunner.query(`
            CREATE TABLE "payments" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "transaction_reference" character varying(255) NOT NULL,
                "amount" numeric(10, 2) NOT NULL,
                "currency" character varying(3) NOT NULL DEFAULT 'NGN',
                "status" "public"."payment_status" NOT NULL DEFAULT 'pending',
                "payment_type" "public"."payment_type" NOT NULL,
                "quantity" integer,
                "metadata" jsonb,
                "user_id" uuid,
                "plan_id" uuid,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "deleted_at" TIMESTAMP,
                CONSTRAINT "UQ_ed7060f04402306c58a2f47bd74" UNIQUE ("transaction_reference"),
                CONSTRAINT "PK_197ab7af18c93fbb0c9b28b4a59" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            ALTER TABLE "plans"
            ADD "metadata" jsonb
        `);
        await queryRunner.query(`
            ALTER TABLE "users"
            ALTER COLUMN "tasks_left" DROP NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "tasks" DROP CONSTRAINT "FK_db55af84c226af9dce09487b61b"
        `);
        await queryRunner.query(`
            ALTER TABLE "tasks"
            ALTER COLUMN "user_id" DROP NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "tasks"
            ADD CONSTRAINT "FK_db55af84c226af9dce09487b61b" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "payments"
            ADD CONSTRAINT "FK_427785468fb7d2733f59e7d7d39" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "payments"
            ADD CONSTRAINT "FK_f9b6a4c3196864cdd91b1a440ee" FOREIGN KEY ("plan_id") REFERENCES "plans"("id") ON DELETE
            SET NULL ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "payments" DROP CONSTRAINT "FK_f9b6a4c3196864cdd91b1a440ee"
        `);
        await queryRunner.query(`
            ALTER TABLE "payments" DROP CONSTRAINT "FK_427785468fb7d2733f59e7d7d39"
        `);
        await queryRunner.query(`
            ALTER TABLE "tasks" DROP CONSTRAINT "FK_db55af84c226af9dce09487b61b"
        `);
        await queryRunner.query(`
            ALTER TABLE "tasks"
            ALTER COLUMN "user_id"
            SET NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "tasks"
            ADD CONSTRAINT "FK_db55af84c226af9dce09487b61b" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "users"
            ALTER COLUMN "tasks_left"
            SET NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "plans" DROP COLUMN "metadata"
        `);
        await queryRunner.query(`
            DROP TABLE "payments"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."payment_type"
        `);
        await queryRunner.query(`
            DROP TYPE "public"."payment_status"
        `);
    }

}
</file>

<file path="src/core/db/migrations/1759999999999-add-database-constraints-and-indexes.ts">
import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDatabaseConstraintsAndIndexes1759999999999
  implements MigrationInterface
{
  name = 'AddDatabaseConstraintsAndIndexes1759999999999';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add foreign key constraints
    await queryRunner.query(`
            ALTER TABLE "users"
            ADD CONSTRAINT "FK_users_plan_id"
            FOREIGN KEY ("plan_id") REFERENCES "plans"("id") ON DELETE SET NULL ON UPDATE CASCADE
        `);

    await queryRunner.query(`
            ALTER TABLE "tasks"
            ADD CONSTRAINT "FK_tasks_user_id"
            FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE
        `);

    // Add indexes for performance
    await queryRunner.query(`
            CREATE INDEX "IDX_users_email" ON "users" ("email")
        `);

    await queryRunner.query(`
            CREATE INDEX "IDX_users_verified" ON "users" ("verified")
        `);

    await queryRunner.query(`
            CREATE INDEX "IDX_users_plan_id" ON "users" ("plan_id")
        `);

    await queryRunner.query(`
            CREATE INDEX "IDX_users_created_at" ON "users" ("created_at")
        `);

    await queryRunner.query(`
            CREATE INDEX "IDX_tasks_user_id" ON "tasks" ("user_id")
        `);

    await queryRunner.query(`
            CREATE INDEX "IDX_tasks_status" ON "tasks" ("status")
        `);

    await queryRunner.query(`
            CREATE INDEX "IDX_tasks_due_at" ON "tasks" ("due_at")
        `);

    await queryRunner.query(`
            CREATE INDEX "IDX_tasks_created_at" ON "tasks" ("created_at")
        `);

    await queryRunner.query(`
            CREATE INDEX "IDX_plans_slug" ON "plans" ("slug")
        `);

    await queryRunner.query(`
            CREATE INDEX "IDX_plans_is_subscription" ON "plans" ("is_subscription")
        `);

    // Add check constraints for data integrity
    await queryRunner.query(`
            ALTER TABLE "users"
            ADD CONSTRAINT "CHK_users_tasks_left_non_negative"
            CHECK ("tasks_left" IS NULL OR "tasks_left" >= 0)
        `);

    await queryRunner.query(`
            ALTER TABLE "plans"
            ADD CONSTRAINT "CHK_plans_price_non_negative"
            CHECK ("price" >= 0)
        `);

    await queryRunner.query(`
            ALTER TABLE "plans"
            ADD CONSTRAINT "CHK_plans_task_limit_positive"
            CHECK ("task_limit" IS NULL OR "task_limit" > 0)
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop check constraints
    await queryRunner.query(`
            ALTER TABLE "plans" DROP CONSTRAINT "CHK_plans_task_limit_positive"
        `);

    await queryRunner.query(`
            ALTER TABLE "plans" DROP CONSTRAINT "CHK_plans_price_non_negative"
        `);

    await queryRunner.query(`
            ALTER TABLE "users" DROP CONSTRAINT "CHK_users_tasks_left_non_negative"
        `);

    // Drop indexes
    await queryRunner.query(`DROP INDEX "IDX_plans_is_subscription"`);
    await queryRunner.query(`DROP INDEX "IDX_plans_slug"`);
    await queryRunner.query(`DROP INDEX "IDX_tasks_created_at"`);
    await queryRunner.query(`DROP INDEX "IDX_tasks_due_at"`);
    await queryRunner.query(`DROP INDEX "IDX_tasks_status"`);
    await queryRunner.query(`DROP INDEX "IDX_tasks_user_id"`);
    await queryRunner.query(`DROP INDEX "IDX_users_created_at"`);
    await queryRunner.query(`DROP INDEX "IDX_users_plan_id"`);
    await queryRunner.query(`DROP INDEX "IDX_users_verified"`);
    await queryRunner.query(`DROP INDEX "IDX_users_email"`);

    // Drop foreign key constraints
    await queryRunner.query(`
            ALTER TABLE "tasks" DROP CONSTRAINT "FK_tasks_user_id"
        `);

    await queryRunner.query(`
            ALTER TABLE "users" DROP CONSTRAINT "FK_users_plan_id"
        `);
  }
}
</file>

<file path="src/core/db/db.module.ts">
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import dbConfig from '../config/db.config';

@Module({
  imports: [TypeOrmModule.forRootAsync(dbConfig.asProvider())],
})
export class DbModule {}
</file>

<file path="src/core/redis/redis.module.ts">
import { Inject, Module, OnModuleDestroy } from '@nestjs/common';
import Redis from 'ioredis';
import redisConfig, { REDIS_CLIENT } from '../config/redis.config';
import { RedisService } from './redis.service';

@Module({
  providers: [
    {
      provide: REDIS_CLIENT,
      useFactory: () => new Redis(redisConfig),
    },
    RedisService,
  ],
  exports: [RedisService],
})
export class RedisModule implements OnModuleDestroy {
  constructor(@Inject(REDIS_CLIENT) private readonly redis: Redis) {}

  async onModuleDestroy(): Promise<void> {
    try {
      await this.redis.quit();
    } catch {
      this.redis.disconnect();
    }
  }
}
</file>

<file path="src/core/redis/redis.service.ts">
import { Inject, Injectable } from '@nestjs/common';
import type Redis from 'ioredis';
import { REDIS_CLIENT } from '../config/redis.config';

@Injectable()
export class RedisService {
  constructor(@Inject(REDIS_CLIENT) private readonly redis: Redis) {}

  async setRefreshToken(
    userId: string,
    token: string,
    ttl: number,
  ): Promise<void> {
    await this.redis.set(this.getKey(userId), token, 'EX', ttl);
  }

  async getRefreshToken(userId: string): Promise<string | null> {
    return this.redis.get(this.getKey(userId));
  }

  async invalidateRefreshToken(userId: string): Promise<void> {
    await this.redis.del(this.getKey(userId));
  }

  private getKey(userId: string): string {
    return `refresh:${userId}`;
  }
}
</file>

<file path="src/core/swagger/jwt-cookie-header.ts">
import { ApiResponseOptions } from '@nestjs/swagger';

export const jwtCookieHeader: ApiResponseOptions['headers'] = {
  'Set-Cookie': {
    description: 'JWT authentication cookie',
    schema: {
      type: 'string',
      example:
        'jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...; HttpOnly; Path=/; SameSite=Strict',
    },
  },
};
</file>

<file path="src/modules/auth/decorators/active-user.decorator.ts">
import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { Request } from 'express';
import type { IRequestUser } from '../../users/interfaces/user.interface';

export const ActiveUser = createParamDecorator(
  (field: keyof IRequestUser, ctx: ExecutionContext) => {
    const { user } = ctx.switchToHttp().getRequest<Request>();

    return field ? user?.[field] : user;
  },
);
</file>

<file path="src/modules/auth/decorators/public.decorator.ts">
import { SetMetadata } from '@nestjs/common';

export const IS_PUBLIC_KEY = 'isPublic';
export const Public = () => SetMetadata(IS_PUBLIC_KEY, true);
</file>

<file path="src/modules/auth/decorators/roles.decorator.ts">
import { SetMetadata } from '@nestjs/common';
import { UserRole } from '../../users/enums/roles.enum';

export const ROLES_KEY = 'roles';
export const Roles = (...roles: UserRole[]) => SetMetadata(ROLES_KEY, roles);
</file>

<file path="src/modules/auth/dto/auth.dto.ts">
import { PickType } from '@nestjs/swagger';
import { CreateUserDto } from '../../users/dto/create-user.dto';

export class AuthDto extends PickType(CreateUserDto, [
  'email',
  'password',
] as const) {}
</file>

<file path="src/modules/auth/dto/change-password.dto.ts">
import { IsNotEmpty, IsString, IsStrongPassword } from 'class-validator';

export class ChangePasswordDto {
  /**
   * Current password
   * @example "Current_Password123"
   */
  @IsString()
  @IsNotEmpty()
  currentPassword: string;

  /**
   * New password
   * Password must meet the following criteria:
   * - at least 8 characters long
   * - at least one lowercase letter
   * - at least one uppercase letter
   * - at least one number
   * - at least one symbol
   * @example "New_Password123"
   */
  @IsString()
  @IsNotEmpty()
  @IsStrongPassword(
    {
      minLength: 8,
      minLowercase: 1,
      minUppercase: 1,
      minNumbers: 1,
      minSymbols: 1,
    },
    {
      message:
        'Password must be at least 8 characters long, with at least one lowercase letter, one uppercase letter, one number, and one symbol.',
    },
  )
  newPassword: string;
}
</file>

<file path="src/modules/auth/dto/refresh-token.dto.ts">
import { IsJWT } from 'class-validator';

export class RefreshTokenDto {
  /**
   * Refresh Token (JWT)
   * @example abcdefghijklmnopqrstuvwxyz1234567890
   */
  @IsJWT()
  refreshToken: string;
}
</file>

<file path="src/modules/auth/dto/resend-verification.dto.ts">
import { IsEmail, IsNotEmpty } from 'class-validator';

export class ResendVerificationDto {
  /**
   * User email address
   * @example "<EMAIL>"
   */
  @IsEmail()
  @IsNotEmpty({ message: 'Email is required.' })
  email: string;
}
</file>

<file path="src/modules/auth/dto/verify-email.dto.ts">
import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString, Length } from 'class-validator';

export class VerifyEmailDto {
  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: '6-digit verification code',
    example: '123456',
    minLength: 6,
    maxLength: 6,
  })
  @IsString()
  @Length(6, 6)
  @IsNotEmpty()
  code: string;
}
</file>

<file path="src/modules/auth/guards/jwt-auth.guard.ts">
import { ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthGuard } from '@nestjs/passport';
import { Observable } from 'rxjs';
import { IS_PUBLIC_KEY } from '../decorators/public.decorator';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(private reflector: Reflector) {
    super();
  }

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    if (isPublic) return true;

    return super.canActivate(context);
  }
}
</file>

<file path="src/modules/auth/guards/local-auth.guard.ts">
import { Injectable } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

@Injectable()
export class LocalAuthGuard extends AuthGuard('local') {}
</file>

<file path="src/modules/auth/guards/role.guard.ts">
import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';

import { UserRole } from '../../users/enums/roles.enum';
import { ROLES_KEY } from '../decorators/roles.decorator';

@Injectable()
export class RoleGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<UserRole[]>(
      ROLES_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!requiredRoles) return true;

    const { user } = context.switchToHttp().getRequest<Request>();
    if (user?.role === UserRole.ADMIN) return true;

    return requiredRoles.some((role) => user?.role === role);
  }
}
</file>

<file path="src/modules/auth/guards/session.guard.ts">
import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Request } from 'express';

@Injectable()
export class SessionGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();
    return request.isAuthenticated();
  }
}
</file>

<file path="src/modules/auth/strategies/jwt.strategy.ts">
import { Inject, Injectable } from '@nestjs/common';
import type { ConfigType } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';

import { IPayload } from 'src/core/common/interfaces/payload.interface';
import { RequestWithCookies } from 'src/core/common/interfaces/request-with-cookies.interface';
import jwtConfig from 'src/core/config/jwt.config';
import tokensConfig from 'src/core/config/tokens.config';
import { AuthService } from '../auth.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    @Inject(jwtConfig.KEY)
    readonly config: ConfigType<typeof jwtConfig>,
    private readonly authService: AuthService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromExtractors([
        JwtStrategy.extractJwtFromCookie,
      ]),
      ignoreExpiration: false,
      secretOrKey: config.secret,
    });
  }

  async validate(payload: IPayload) {
    return this.authService.validateJwt(payload);
  }

  private static extractJwtFromCookie(
    this: void,
    req: RequestWithCookies,
  ): string | null {
    return req.cookies[tokensConfig.access] || null;
  }
}
</file>

<file path="src/modules/auth/strategies/local.strategy.ts">
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-local';
import { AuthService } from '../auth.service';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(private readonly authService: AuthService) {
    super({ usernameField: 'email' });
  }

  async validate(email: string, password: string) {
    const user = await this.authService.validateLocal({ email, password });
    if (!user) throw new UnauthorizedException('Invalid credentials');

    return user;
  }
}
</file>

<file path="src/modules/email/interfaces/email-options.ts">
export interface EmailOptions {
  to: string;
  subject: string;
  text?: string;
  html?: string;
}
</file>

<file path="src/modules/email/templates/verification.hbs">
<html lang='en'>
  <head>
    <meta charset='UTF-8' />
    <meta name='viewport' content='width=device-width, initial-scale=1.0' />
    <title>Email Verification</title>
    <style>
      body {
        font-family:
          -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
          'Helvetica Neue', Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
      }
      .container {
        background-color: #ffffff;
        border-radius: 8px;
        padding: 40px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .header {
        text-align: center;
        margin-bottom: 30px;
      }
      .header h1 {
        color: #2563eb;
        margin: 0;
      }
      .code-container {
        background-color: #f3f4f6;
        border-radius: 8px;
        padding: 30px;
        text-align: center;
        margin: 30px 0;
      }
      .code {
        font-size: 32px;
        font-weight: bold;
        letter-spacing: 8px;
        color: #1f2937;
        margin: 0;
      }
      .footer {
        text-align: center;
        margin-top: 30px;
        color: #6b7280;
        font-size: 14px;
      }
      .warning {
        background-color: #fef3c7;
        border-left: 4px solid #f59e0b;
        padding: 12px;
        margin-top: 20px;
        border-radius: 4px;
      }
      .verify-button {
        display: inline-block;
        background-color: #2563eb;
        color: white;
        padding: 12px 24px;
        text-decoration: none;
        border-radius: 6px;
        font-weight: bold;
        margin: 20px 0;
        border: none;
        cursor: pointer;
        font-size: 16px;
      }
      .verify-button:hover {
        background-color: #1d4ed8;
      }
      .endpoint-info {
        background-color: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        padding: 16px;
        margin: 20px 0;
        font-family: 'Courier New', monospace;
        font-size: 14px;
      }
      .endpoint-info strong {
        color: #1f2937;
      }
    </style>
  </head>
  <body>
    <div class='container'>
      <div class='header'>
        <h1>Email Verification</h1>
      </div>

      <p>Hello{{#if name}} {{name}}{{/if}},</p>

      <p>
        Thank you for signing up with Aiki! To complete your registration,
        please use the verification code below:
      </p>

      <div class='code-container'>
        <p class='code'>{{code}}</p>
      </div>

      <p>
        This verification code will expire in
        <strong>10 minutes</strong>.
      </p>

      <div class='endpoint-info'>
        <p style='margin: 0 0 10px 0;'><strong>API Endpoint:</strong></p>
        <p style='margin: 0 0 10px 0;'>POST /api/v1/auth/verify-email</p>
        <p style='margin: 0;'><strong>Payload:</strong>
          {"email": "your-email", "code": "{{code}}"}</p>
      </div>

      <div style='text-align: center;'>
        <p><strong>Use this verification code with the API endpoint above, or
            copy the code and use your preferred API client.</strong></p>
        <p style='font-size: 14px; color: #6b7280;'>
          After verification, you can sign in at:
          <strong>/api/v1/auth/signin</strong>
        </p>
      </div>

      <div class='warning'>
        <p style='margin: 0;'>
          <strong>Security Notice:</strong>
          If you didn't request this verification code, please ignore this
          email.
        </p>
      </div>

      <div class='footer'>
        <p>Best regards,</p>
        <strong>The Aiki Team</strong>
        <p style='font-size: 12px; color: #9ca3af;'>
          This is an automated email, please do not reply.
        </p>
      </div>
    </div>
  </body>
</html>
</file>

<file path="src/modules/health/health.module.ts">
import { Module } from '@nestjs/common';

import { TerminusModule } from '@nestjs/terminus';
import { HealthController } from './health.controller';
import { HealthService } from './health.service';

@Module({
  imports: [TerminusModule],
  controllers: [HealthController],
  providers: [HealthService],
})
export class HealthModule {}
</file>

<file path="src/modules/payments/dto/initialize-payment.dto.ts">
import { IsInt, IsNotEmpty, IsOptional, IsUUID, Min } from 'class-validator';

export class InitializePaymentDto {
  /**
   * ID of the plan to purchase
   * @example "550e8400-e29b-41d4-a716-************"
   */
  @IsUUID()
  @IsNotEmpty()
  plan_id: string;

  /**
   * Quantity to purchase (for Focus plan)
   * @example 10
   */
  @IsInt()
  @Min(1)
  @IsOptional()
  quantity?: number;
}
</file>

<file path="src/modules/payments/dto/update-payment.dto.ts">
import { PartialType } from '@nestjs/swagger';
import { CreatePaymentDto } from './create-payment.dto';

export class UpdatePaymentDto extends PartialType(CreatePaymentDto) {}
</file>

<file path="src/modules/payments/dto/verify-payment.dto.ts">
import { IsNotEmpty, IsString } from 'class-validator';

export class VerifyPaymentDto {
  /**
   * Paystack transaction reference
   * @example "re4lyvq3s3"
   */
  @IsString()
  @IsNotEmpty()
  reference: string;
}
</file>

<file path="src/modules/payments/enums/payment-status.enum.ts">
export enum PaymentStatus {
  PENDING = 'pending',
  SUCCESS = 'success',
  FAILED = 'failed',
}
</file>

<file path="src/modules/payments/enums/payment-type.enum.ts">
export enum PaymentType {
  ONE_TIME = 'one_time',
  SUBSCRIPTION = 'subscription',
}
</file>

<file path="src/modules/payments/interfaces/initialize-response.interface.ts">
export interface IPaymentInitializeResponse {
  status: boolean;
  message: string;
  data: {
    access_code: string;
    authorization_url: string;
    reference: string;
  };
}
</file>

<file path="src/modules/payments/interfaces/subscription-response.interface.ts">
export interface IPaymentSubscriptionResponse {
  status: boolean;
  message: string;
  data: {
    customer: number;
    plan: number;
    integration: number;
    domain: string;
    start: number;
    status: string;
    quantity: number;
    amount: number;
    subscription_code: string;
    email_token: string;
    authorization: any;
  };
}
</file>

<file path="src/modules/payments/interfaces/verify-response.interface.ts">
export interface IPaymentVerificationResponse {
  status: boolean;
  message: string;
  data: {
    id: number;
    domain: string;
    status: string;
    reference: string;
    amount: number;
    fees: number;
    currency: string;
    customer: any;
    message: string | null;
    gateway_response: string;
    paid_at: string;
    created_at: string;
    channel: string;
    ip_address: string;
    metadata: any;
    authorization: any;
    plan: any;
  };
}
</file>

<file path="src/modules/plans/dto/create-plan.dto.ts">
export class CreatePlanDto {}
</file>

<file path="src/modules/plans/dto/update-plan.dto.ts">
import { PartialType } from '@nestjs/swagger';
import { CreatePlanDto } from './create-plan.dto';

export class UpdatePlanDto extends PartialType(CreatePlanDto) {}
</file>

<file path="src/modules/tasks/enums/task-status.enum.ts">
export enum TaskStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  DONE = 'done',
}
</file>

<file path="src/modules/users/dto/create-user.dto.ts">
import {
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsPhoneNumber,
  IsString,
  IsStrongPassword,
} from 'class-validator';

import { UserRole } from '../enums/roles.enum';

export class CreateUserDto {
  /**
   * User email
   * @example "<EMAIL>"
   */
  @IsEmail()
  @IsNotEmpty({ message: 'Email is required.' })
  email: string;

  /**
   * Password must meet the following criteria:
   * - at least 8 characters long
   * - at least one lowercase letter
   * - at least one uppercase letter
   * - at least one number
   * - at least one symbol
   * @example "Alpha123$!@"
   */
  @IsString()
  @IsNotEmpty()
  @IsStrongPassword(
    {
      minLength: 8,
      minLowercase: 1,
      minUppercase: 1,
      minNumbers: 1,
      minSymbols: 1,
    },
    {
      message:
        'Password must contain at least one lowercase letter, one uppercase letter, one number, and one symbol.',
    },
  )
  password: string;

  /**
   * User full name
   * @example "Owolabi Omoninakuna"
   */
  @IsString()
  @IsOptional()
  name?: string;

  /**
   * User phone number
   * @example "+2349012345678"
   */
  @IsPhoneNumber('NG', {
    message: 'Phone number must be a valid Nigerian phone number.',
  })
  @IsOptional()
  phone?: string;

  /**
   * User bio
   * @example "A passionate software developer."
   */
  @IsString()
  @IsOptional()
  bio?: string;

  /**
   * User role
   * @example "user"
   */
  @IsString()
  @IsEnum(() => UserRole, { message: 'Role must be either "admin" or "user".' })
  role: UserRole = UserRole.USER;

  /**
   * User's last login date and time
   * @example "2023-10-01T12:00:00Z"
   */
  @IsOptional()
  lastLoginAt?: Date;
}
</file>

<file path="src/modules/users/dto/update-user.dto.ts">
import { PartialType } from '@nestjs/swagger';
import { CreateUserDto } from './create-user.dto';

export class UpdateUserDto extends PartialType(CreateUserDto) {}
</file>

<file path="src/modules/users/enums/roles.enum.ts">
export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
}
</file>

<file path="src/modules/users/interfaces/user.interface.ts">
import { User } from '../entities/user.entity';
import { UserRole } from '../enums/roles.enum';

declare module 'express-serve-static-core' {
  interface Request {
    user?: User;
  }
}

export interface IRequestUser {
  /**
   * User ID
   * @example 123e4567-e89b-12d3-a456-************
   */
  readonly id: string;

  /**
   * User Email
   * @example <EMAIL>
   */
  readonly email: string;

  /**
   * User Role
   * @example Reader
   */
  readonly role: UserRole;
}
</file>

<file path="test/app.e2e-spec.ts">
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { App } from 'supertest/types';
import { AppModule } from './../src/app.module';

describe('AppController (e2e)', () => {
  let app: INestApplication<App>;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  it('/ (GET)', () => {
    return request(app.getHttpServer())
      .get('/')
      .expect(200)
      .expect('Hello World!');
  });
});
</file>

<file path="test/jest-e2e.json">
{
  "moduleFileExtensions": ["js", "json", "ts"],
  "rootDir": ".",
  "testEnvironment": "node",
  "testRegex": ".e2e-spec.ts$",
  "transform": {
    "^.+\\.(t|j)s$": "ts-jest"
  }
}
</file>

<file path=".dockerignore">
# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
jspm_packages/

# TypeScript v1 declaration files
typings/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.local
.env.production

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Development files
*.log
*.tmp
*.temp
temp/
tmp/

# Build output
dist/
build/

# Test files
coverage/
test-results/
*.test.ts
*.spec.ts
test/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore
README.md
.github/

# Docker
Dockerfile
.dockerignore
compose*.yml

# Bun
.bun
</file>

<file path=".gitignore">
# compiled output
/dist
/node_modules
/build

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# temp directory
.temp
.tmp

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json
\n.env
</file>

<file path="compose.dev.yml">
name: aiki
services:
  aiki-api:
    container_name: aiki-api-dev
    # image: ghcr.io/claudiusayadi/aiki-api:latest
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        PUID: ${PUID:-1000}
        PGID: ${PGID:-1000}
    user: "$PUID:$PGID"
    restart: unless-stopped
    ports:
      - "$API_PORT:$API_PORT"
    env_file:
      - ./.env
    environment:
      API_PORT: $API_PORT
      API_URL: $API_URL
      DB_URL: $DB_URL
      EMAIL_FROM: $EMAIL_FROM
      EMAIL_HOST: $EMAIL_HOST
      EMAIL_PASSWORD: $EMAIL_PASSWORD
      EMAIL_PORT: $EMAIL_PORT
      EMAIL_SENDER: $EMAIL_SENDER
      EMAIL_USERNAME: $EMAIL_USERNAME
      HTTP_MAX_REDIRECTS: $HTTP_MAX_REDIRECTS
      HTTP_TIMEOUT: $HTTP_TIMEOUT
      JWT_ACCESS_TOKEN_TTL: $JWT_ACCESS_TOKEN_TTL
      JWT_AUDIENCE: $JWT_AUDIENCE
      JWT_ISSUER: $JWT_ISSUER
      JWT_REFRESH_TOKEN_TTL: $JWT_REFRESH_TOKEN_TTL
      JWT_SECRET: $JWT_SECRET
      NODE_ENV: $NODE_ENV
      PAYSTACK_SECRET_KEY: $PAYSTACK_SECRET_KEY
      PAYSTACK_PUBLIC_KEY: $PAYSTACK_PUBLIC_KEY
      PAYSTACK_BASE_URL: $PAYSTACK_BASE_URL
      PAYSTACK_SUCCESS_URL: $PAYSTACK_SUCCESS_URL
      REDIS_HOST: $REDIS_HOST
      REDIS_PORT: $REDIS_PORT
      SMTP_LOGIN: $SMTP_LOGIN
      SMTP_PASSWORD: $SMTP_PASSWORD
      SMTP_PORT: $SMTP_PORT
      SMTP_SERVER: $SMTP_SERVER
      THROTTLER_LIMIT: $THROTTLER_LIMIT
      THROTTLER_TTL: $THROTTLER_TTL
      TZ: $TZ
    networks:
      - db
    volumes:
      - ./:/usr/src/api
      - api_node_modules:/usr/src/api/node_modules
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:$API_PORT/health"]
      interval: 60s
      timeout: 30s
      retries: 3

networks:
  db:
    external: true

volumes:
  api_node_modules:
</file>

<file path="Dockerfile.dev">
FROM node:20-alpine AS base

WORKDIR /usr/src/api

# Copy only the api package manifest and the root lockfile, then install.
COPY package.json ./
COPY yarn.lock* ./

RUN yarn install

ENV NODE_ENV=development
# Copy only the api workspace sources (avoid overwriting package.json)
COPY . ./

EXPOSE ${API_PORT}

CMD [ "yarn", "run", "start:dev" ]
</file>

<file path="eslint.config.mjs">
// @ts-check
import eslint from '@eslint/js';
import eslintPluginPrettierRecommended from 'eslint-plugin-prettier/recommended';
import globals from 'globals';
import tseslint from 'typescript-eslint';

export default tseslint.config(
  {
    ignores: ['eslint.config.mjs'],
  },
  eslint.configs.recommended,
  ...tseslint.configs.recommendedTypeChecked,
  eslintPluginPrettierRecommended,
  {
    languageOptions: {
      globals: {
        ...globals.node,
        ...globals.jest,
      },
      sourceType: 'commonjs',
      parserOptions: {
        projectService: true,
        tsconfigRootDir: import.meta.dirname,
      },
    },
  },
  {
    rules: {
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-floating-promises': 'warn',
      '@typescript-eslint/no-unsafe-argument': 'warn'
    },
  },
);
</file>

<file path="README.md">
<p align="center">
  <a href="http://nestjs.com/" target="blank"><img src="https://nestjs.com/img/logo-small.svg" width="120" alt="Nest Logo" /></a>
</p>

[circleci-image]: https://img.shields.io/circleci/build/github/nestjs/nest/master?token=abc123def456
[circleci-url]: https://circleci.com/gh/nestjs/nest

  <p align="center">A progressive <a href="http://nodejs.org" target="_blank">Node.js</a> framework for building efficient and scalable server-side applications.</p>
    <p align="center">
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/v/@nestjs/core.svg" alt="NPM Version" /></a>
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/l/@nestjs/core.svg" alt="Package License" /></a>
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/dm/@nestjs/common.svg" alt="NPM Downloads" /></a>
<a href="https://circleci.com/gh/nestjs/nest" target="_blank"><img src="https://img.shields.io/circleci/build/github/nestjs/nest/master" alt="CircleCI" /></a>
<a href="https://discord.gg/G7Qnnhy" target="_blank"><img src="https://img.shields.io/badge/discord-online-brightgreen.svg" alt="Discord"/></a>
<a href="https://opencollective.com/nest#backer" target="_blank"><img src="https://opencollective.com/nest/backers/badge.svg" alt="Backers on Open Collective" /></a>
<a href="https://opencollective.com/nest#sponsor" target="_blank"><img src="https://opencollective.com/nest/sponsors/badge.svg" alt="Sponsors on Open Collective" /></a>
  <a href="https://paypal.me/kamilmysliwiec" target="_blank"><img src="https://img.shields.io/badge/Donate-PayPal-ff3f59.svg" alt="Donate us"/></a>
    <a href="https://opencollective.com/nest#sponsor"  target="_blank"><img src="https://img.shields.io/badge/Support%20us-Open%20Collective-41B883.svg" alt="Support us"></a>
  <a href="https://twitter.com/nestframework" target="_blank"><img src="https://img.shields.io/twitter/follow/nestframework.svg?style=social&label=Follow" alt="Follow us on Twitter"></a>
</p>
  <!--[![Backers on Open Collective](https://opencollective.com/nest/backers/badge.svg)](https://opencollective.com/nest#backer)
  [![Sponsors on Open Collective](https://opencollective.com/nest/sponsors/badge.svg)](https://opencollective.com/nest#sponsor)-->

## Description

[Nest](https://github.com/nestjs/nest) framework TypeScript starter repository.

## Project setup

```bash
$ yarn install
```

## Compile and run the project

```bash
# development
$ yarn run start

# watch mode
$ yarn run start:dev

# production mode
$ yarn run start:prod
```

## Run tests

```bash
# unit tests
$ yarn run test

# e2e tests
$ yarn run test:e2e

# test coverage
$ yarn run test:cov
```

## Deployment

When you're ready to deploy your NestJS application to production, there are some key steps you can take to ensure it runs as efficiently as possible. Check out the [deployment documentation](https://docs.nestjs.com/deployment) for more information.

If you are looking for a cloud-based platform to deploy your NestJS application, check out [Mau](https://mau.nestjs.com), our official platform for deploying NestJS applications on AWS. Mau makes deployment straightforward and fast, requiring just a few simple steps:

```bash
$ yarn install -g @nestjs/mau
$ mau deploy
```

With Mau, you can deploy your application in just a few clicks, allowing you to focus on building features rather than managing infrastructure.

## Resources

Check out a few resources that may come in handy when working with NestJS:

- Visit the [NestJS Documentation](https://docs.nestjs.com) to learn more about the framework.
- For questions and support, please visit our [Discord channel](https://discord.gg/G7Qnnhy).
- To dive deeper and get more hands-on experience, check out our official video [courses](https://courses.nestjs.com/).
- Deploy your application to AWS with the help of [NestJS Mau](https://mau.nestjs.com) in just a few clicks.
- Visualize your application graph and interact with the NestJS application in real-time using [NestJS Devtools](https://devtools.nestjs.com).
- Need help with your project (part-time to full-time)? Check out our official [enterprise support](https://enterprise.nestjs.com).
- To stay in the loop and get updates, follow us on [X](https://x.com/nestframework) and [LinkedIn](https://linkedin.com/company/nestjs).
- Looking for a job, or have a job to offer? Check out our official [Jobs board](https://jobs.nestjs.com).

## Support

Nest is an MIT-licensed open source project. It can grow thanks to the sponsors and support by the amazing backers. If you'd like to join them, please [read more here](https://docs.nestjs.com/support).

## Stay in touch

- Author - [Kamil Myśliwiec](https://twitter.com/kammysliwiec)
- Website - [https://nestjs.com](https://nestjs.com/)
- Twitter - [@nestframework](https://twitter.com/nestframework)

## License

Nest is [MIT licensed](https://github.com/nestjs/nest/blob/master/LICENSE).
</file>

<file path="tsconfig.build.json">
{
  "extends": "./tsconfig.json",
  "exclude": ["node_modules", "test", "dist", "**/*spec.ts"]
}
</file>

<file path="src/core/config/data-source.ts">
import { DataSource } from 'typeorm';
import { ApiConfig } from './app.config';

export default new DataSource({
  type: 'postgres',
  url: ApiConfig.DB_URL,
  entities: ['dist/src/modules/**/*.entity.js'],
  migrations: ['dist/src/core/db/migrations/*.js'],
});
</file>

<file path="src/core/config/email.config.ts">
import { MailerOptions } from '@nestjs-modules/mailer';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { registerAs } from '@nestjs/config';
import { join } from 'path';
import { ApiConfig } from './app.config';

export default registerAs('email', () => {
  const config = {
    transport: {
      host: ApiConfig.EMAIL_HOST,
      port: ApiConfig.EMAIL_PORT,
      secure: ApiConfig.EMAIL_SECURE,
      auth: {
        user: ApiConfig.EMAIL_USERNAME,
        pass: ApiConfig.EMAIL_PASSWORD,
      },
    },
    defaults: {
      from: {
        name: ApiConfig.EMAIL_FROM,
        address: ApiConfig.EMAIL_SENDER,
      },
    },
    template: {
      dir: join(process.cwd(), 'src/modules/email/templates'),
      adapter: new HandlebarsAdapter(),
      options: {
        strict: true,
      },
    },
    verificationCodeTtl: ApiConfig.VERIFICATION_CODE_TTL * 1000,
  } as const satisfies MailerOptions & { verificationCodeTtl: number };

  return config;
});
</file>

<file path="src/core/config/providers.config.ts">
import { ValidationPipe } from '@nestjs/common';
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';
import { ThrottlerGuard } from '@nestjs/throttler';

import { JwtAuthGuard } from 'src/modules/auth/guards/jwt-auth.guard';
import { RoleGuard } from 'src/modules/auth/guards/role.guard';

import { GlobalExceptionsFilter } from '../common/filters/global-exceptions.filter';
import { Traffic } from '../common/interceptors/logging.interceptor';
import { ResponseInterceptor } from '../common/interceptors/response.interceptor';
import { VALIDATION_PIPE_OPTIONS } from '../common/utils/common.constants';

export const apiProviders = [
  {
    provide: APP_GUARD,
    useClass: ThrottlerGuard,
  },
  {
    provide: APP_GUARD,
    useClass: JwtAuthGuard,
  },
  {
    provide: APP_GUARD,
    useClass: RoleGuard,
  },
  {
    provide: APP_PIPE,
    useValue: new ValidationPipe(VALIDATION_PIPE_OPTIONS),
  },
  {
    provide: APP_FILTER,
    useClass: GlobalExceptionsFilter,
  },
  {
    provide: APP_INTERCEPTOR,
    useClass: Traffic,
  },
  {
    provide: APP_INTERCEPTOR,
    useClass: ResponseInterceptor,
  },
];
</file>

<file path="src/modules/auth/auth.controller.ts">
import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Patch,
  Post,
  Req,
  Res,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiConflictResponse,
  ApiCookieAuth,
  ApiCreatedResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import type { Response } from 'express';

import type { RequestWithCookies } from 'src/core/common/interfaces/request-with-cookies.interface';
import cookieConfig from 'src/core/config/cookie.config';
import tokensConfig from 'src/core/config/tokens.config';
import { jwtCookieHeader } from 'src/core/swagger/jwt-cookie-header';
import type { IRequestUser } from '../users/interfaces/user.interface';
import { AuthService } from './auth.service';
import { ActiveUser } from './decorators/active-user.decorator';
import { Public } from './decorators/public.decorator';
import { AuthDto } from './dto/auth.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { ResendVerificationDto } from './dto/resend-verification.dto';
import { VerifyEmailDto } from './dto/verify-email.dto';
import { LocalAuthGuard } from './guards/local-auth.guard';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @ApiOperation({ summary: 'Sign up user' })
  @ApiCreatedResponse({ description: 'User registered successfully' })
  @ApiConflictResponse({ description: 'Account already exists' })
  @Public()
  @Post('signup')
  async signup(@Body() dto: AuthDto) {
    return this.authService.signup(dto);
  }

  @ApiCookieAuth(tokensConfig.access)
  @ApiOperation({ summary: 'Sign in user' })
  @ApiOkResponse({
    description: 'User signed in successfully',
    headers: jwtCookieHeader,
  })
  @ApiUnauthorizedResponse({ description: 'Invalid credentials' })
  @HttpCode(HttpStatus.OK)
  @UseGuards(LocalAuthGuard)
  @Public()
  @Post('signin')
  async signin(
    @Body() dto: AuthDto,
    @ActiveUser() user: IRequestUser,
    @Res({ passthrough: true }) res: Response,
  ) {
    const { accessToken, refreshToken } = await this.authService.signin(user);
    res.cookie(tokensConfig.access, accessToken, cookieConfig.access);
    res.cookie(tokensConfig.refresh, refreshToken, cookieConfig.refresh);
    return { message: 'Signed in successfully', user };
  }

  @ApiOperation({ summary: 'Sign out user' })
  @ApiOkResponse({ description: 'User signed out successfully' })
  @HttpCode(HttpStatus.OK)
  @Public()
  @Post('signout')
  signout(@Res({ passthrough: true }) res: Response) {
    res.clearCookie(tokensConfig.access, cookieConfig.access);
    res.clearCookie(tokensConfig.refresh, cookieConfig.refresh);
    return { message: 'Signed out successfully' };
  }

  @ApiOperation({ summary: 'Refresh tokens' })
  @ApiOkResponse({ description: 'Tokens refreshed successfully' })
  @ApiUnauthorizedResponse({ description: 'Invalid refresh token' })
  @Post('refresh')
  async refresh(
    @ActiveUser() user: IRequestUser,
    @Req() req: RequestWithCookies,
    @Res({ passthrough: true }) res: Response,
  ) {
    const refreshToken = req?.cookies[tokensConfig.refresh];
    if (!refreshToken) {
      throw new UnauthorizedException('No refresh token provided');
    }

    const { accessToken, refreshToken: newRefreshToken } =
      await this.authService.refreshTokens(user.id, refreshToken);
    res.cookie(tokensConfig.access, accessToken, cookieConfig.access);
    res.cookie(tokensConfig.refresh, newRefreshToken, cookieConfig.refresh);
    return { message: 'Tokens refreshed successfully' };
  }

  @ApiOperation({ summary: 'Change password' })
  @ApiOkResponse({ description: 'Password changed successfully' })
  @ApiUnauthorizedResponse({ description: 'Invalid current password' })
  @ApiNotFoundResponse({ description: 'User not found' })
  @Patch('change-password')
  async changePassword(
    @ActiveUser() user: IRequestUser,
    @Body() dto: ChangePasswordDto,
  ) {
    return this.authService.changePassword(user.id, dto);
  }

  @ApiOperation({ summary: 'Verify email with code' })
  @ApiOkResponse({ description: 'Email verified successfully' })
  @ApiBadRequestResponse({
    description: 'Invalid or expired verification code',
  })
  @ApiNotFoundResponse({ description: 'User not found' })
  @HttpCode(HttpStatus.OK)
  @Public()
  @Post('verify-email')
  async verifyEmail(@Body() dto: VerifyEmailDto) {
    return this.authService.verifyEmail(dto);
  }

  @ApiOperation({ summary: 'Resend verification code' })
  @ApiOkResponse({ description: 'Verification code sent successfully' })
  @ApiBadRequestResponse({ description: 'Email already verified' })
  @ApiNotFoundResponse({ description: 'User not found' })
  @HttpCode(HttpStatus.OK)
  @Public()
  @Post('resend-verification')
  async resendVerification(@Body() dto: ResendVerificationDto) {
    return this.authService.resendVerificationCode(dto);
  }
}
</file>

<file path="src/modules/auth/auth.module.ts">
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';

import emailConfig from 'src/core/config/email.config';
import jwtConfig from 'src/core/config/jwt.config';
import { RedisModule } from 'src/core/redis/redis.module';

import { PlansModule } from 'src/modules/plans/plans.module';
import { EmailModule } from '../email/email.module';
import { Plan } from '../plans/entities/plan.entity';
import { User } from '../users/entities/user.entity';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { JwtStrategy } from './strategies/jwt.strategy';
import { LocalStrategy } from './strategies/local.strategy';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Plan]),
    PassportModule.register({ session: true }),
    JwtModule.registerAsync(jwtConfig.asProvider()),
    ConfigModule.forFeature(jwtConfig),
    ConfigModule.forFeature(emailConfig),
    RedisModule,
    EmailModule,
    PlansModule,
  ],
  controllers: [AuthController],
  providers: [AuthService, LocalStrategy, JwtStrategy],
  exports: [AuthService],
})
export class AuthModule {}
</file>

<file path="src/modules/email/email.module.ts">
import { MailerModule } from '@nestjs-modules/mailer';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import emailConfig from 'src/core/config/email.config';
import { EmailService } from './email.service';

@Module({
  imports: [
    ConfigModule.forFeature(emailConfig),
    MailerModule.forRootAsync(emailConfig.asProvider()),
  ],
  controllers: [],
  providers: [EmailService],
  exports: [EmailService],
})
export class EmailModule {}
</file>

<file path="src/modules/email/email.service.ts">
import { MailerService } from '@nestjs-modules/mailer';
import { Injectable, Logger } from '@nestjs/common';

import { EmailOptions } from './interfaces/email-options';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  constructor(private readonly mailerService: MailerService) {}

  async sendEmail(options: EmailOptions): Promise<void> {
    try {
      await this.mailerService.sendMail({
        to: options.to,
        subject: options.subject,
        text: options.text,
        html: options.html,
      });

      this.logger.log(`Email sent successfully to ${options.to}`);
    } catch (error) {
      this.logger.error(`Failed to send email to ${options.to}:`, error);
      throw error;
    }
  }

  async sendVerificationEmail(
    email: string,
    code: string,
    name?: string,
  ): Promise<void> {
    try {
      await this.mailerService.sendMail({
        to: email,
        subject: 'Verify Your Email - Aiki',
        template: 'verification',
        context: {
          code,
          name,
        },
      });

      this.logger.log(`Verification email sent successfully to ${email}`);
    } catch (error) {
      this.logger.error(
        `Failed to send verification email to ${email}:`,
        error,
      );
      throw error;
    }
  }
}
</file>

<file path="src/modules/health/health.controller.ts">
import { Controller, Get } from '@nestjs/common';
import { ApiOkResponse, ApiOperation } from '@nestjs/swagger';
import { HealthCheck } from '@nestjs/terminus';

import { Public } from '../auth/decorators/public.decorator';
import { HealthService } from './health.service';

@Controller('health')
export class HealthController {
  constructor(private readonly healthService: HealthService) {}

  @ApiOperation({ summary: 'Health Check' })
  @ApiOkResponse({ description: 'The service is healthy.' })
  @Public()
  @HealthCheck()
  @Get()
  check() {
    return this.healthService.check();
  }

  @ApiOperation({ summary: 'Get detailed metrics' })
  @ApiOkResponse({ description: 'Detailed system metrics.' })
  @Public()
  @Get('metrics')
  getMetrics() {
    return this.healthService.getMetrics();
  }
}
</file>

<file path="src/modules/health/health.service.ts">
import { Injectable, Logger } from '@nestjs/common';
import {
  DiskHealthIndicator,
  HealthCheckService,
  MemoryHealthIndicator,
  TypeOrmHealthIndicator,
} from '@nestjs/terminus';

@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);

  constructor(
    private readonly health: HealthCheckService,
    private readonly db: TypeOrmHealthIndicator,
    private readonly memory: MemoryHealthIndicator,
    private readonly disk: DiskHealthIndicator,
  ) {}

  check() {
    return this.health.check([
      // Database health check
      () => this.db.pingCheck('database'),

      // Memory health check (alert if using more than 300MB)
      () => this.memory.checkHeap('memory_heap', 300 * 1024 * 1024),

      // Disk health check (alert if disk usage > 80%)
      () =>
        this.disk.checkStorage('storage', {
          path: '/',
          thresholdPercent: 0.8,
        }),
    ]);
  }

  async getMetrics() {
    try {
      const healthResult = await this.check();

      // Log health metrics for monitoring
      this.logger.log(
        `Health check completed: ${JSON.stringify(healthResult)}`,
      );

      return {
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        ...healthResult,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Health check failed: ${errorMessage}`);
      throw error;
    }
  }
}
</file>

<file path="src/modules/payments/dto/create-payment.dto.ts">
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
  IsUUID,
} from 'class-validator';

import { PaymentStatus } from '../enums/payment-status.enum';
import { PaymentType } from '../enums/payment-type.enum';

export class CreatePaymentDto {
  /**
   * Unique transaction reference
   * @example 'focus-plan-123-1738944000000'
   */
  @IsString()
  @IsNotEmpty()
  transaction_reference: string;

  /**
   * Payment amount in the specified currency
   * @example 5000
   */
  @IsNumber()
  @IsPositive()
  amount: number;

  /**
   * Currency code (3 letters)
   * @example 'NGN'
   * @default 'NGN'
   */
  @IsString()
  @IsOptional()
  currency?: string;

  /**
   * Payment status
   * @default PaymentStatus.PENDING
   */
  @IsEnum(PaymentStatus)
  @IsOptional()
  status?: PaymentStatus;

  /**
   * Payment type (one-time or subscription)
   * @example PaymentType.ONE_TIME
   */
  @IsEnum(PaymentType)
  @IsNotEmpty()
  payment_type: PaymentType;

  /**
   * Quantity of items (for one-time payments)
   * @example 1
   */
  @IsNumber()
  @IsPositive()
  @IsOptional()
  quantity?: number;

  /**
   * Additional metadata (JSON object)
   * @example { paystack_access_code: 'abc123', custom_field: 'value' }
   */
  @IsOptional()
  metadata?: Record<string, any>;

  /**
   * User ID
   * @example '123e4567-e89b-12d3-a456-************'
   */
  @IsUUID()
  @IsNotEmpty()
  user_id: string;

  /**
   * Plan ID
   * @example '123e4567-e89b-12d3-a456-************'
   */
  @IsUUID()
  @IsOptional()
  plan_id?: string;
}
</file>

<file path="src/modules/payments/entities/payment.entity.ts">
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

import { RegistryDates } from 'src/core/common/dto/registry-dates.dto';
import { Plan } from '../../plans/entities/plan.entity';
import { User } from '../../users/entities/user.entity';
import { PaymentStatus } from '../enums/payment-status.enum';
import { PaymentType } from '../enums/payment-type.enum';

@Entity('payments')
export class Payment {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  transaction_reference: string;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  amount: number;

  @Column({ type: 'varchar', length: 3, default: 'NGN' })
  currency: string;

  @Column({
    type: 'enum',
    enum: PaymentStatus,
    enumName: 'payment_status',
    default: PaymentStatus.PENDING,
  })
  status: PaymentStatus;

  @Column({
    type: 'enum',
    enum: PaymentType,
    enumName: 'payment_type',
  })
  payment_type: PaymentType;

  @Column({ type: 'int', nullable: true })
  quantity?: number;

  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  @ManyToOne(() => User, { onDelete: 'CASCADE', eager: true })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => Plan, { onDelete: 'SET NULL', nullable: true })
  @JoinColumn({ name: 'plan_id' })
  plan?: Plan;

  @Column(() => RegistryDates, { prefix: false })
  registry: RegistryDates;
}
</file>

<file path="src/modules/payments/payments.controller.ts">
import { Body, Controller, Get, Headers, Param, Post } from '@nestjs/common';
import {
  ApiCreatedResponse,
  ApiOkResponse,
  ApiOperation,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { IdDto } from '../../core/common/dto/id.dto';
import { ActiveUser } from '../auth/decorators/active-user.decorator';
import { Public } from '../auth/decorators/public.decorator';
import type { IRequestUser } from '../users/interfaces/user.interface';
import { InitializePaymentDto } from './dto/initialize-payment.dto';
import { VerifyPaymentDto } from './dto/verify-payment.dto';
import { Payment } from './entities/payment.entity';
import { PaymentsService } from './payments.service';

@Controller('payments')
export class PaymentsController {
  constructor(private readonly paymentsService: PaymentsService) {}

  @ApiOperation({
    summary: 'Initialize payment (Focus one-time or Flow subscription)',
  })
  @ApiCreatedResponse({
    description: 'Payment initialized successfully',
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @Post('initialize')
  async initializePayment(
    @ActiveUser() user: IRequestUser,
    @Body() dto: InitializePaymentDto,
  ) {
    return await this.paymentsService.initializePayment(user, dto);
  }

  @ApiOperation({ summary: 'Verify payment and update user tasks_left' })
  @ApiOkResponse({
    description: 'Payment verified successfully',
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @Post('verify')
  async verifyPayment(@Body() dto: VerifyPaymentDto) {
    return await this.paymentsService.verifyPayment(dto);
  }

  @ApiOperation({ summary: 'Handle Paystack webhook (public endpoint)' })
  @Public()
  @Post('webhook')
  async handleWebhook(
    @Body() payload: { event: string; data: { reference: string } },
    @Headers('x-paystack-signature') signature: string,
  ) {
    return await this.paymentsService.handleWebhook(payload, signature);
  }

  @ApiOperation({ summary: 'Get all user payments - owner/admin' })
  @ApiOkResponse({
    description: 'Payments retrieved successfully',
    type: [Payment],
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @Get()
  async findAll(@ActiveUser() user: IRequestUser) {
    return await this.paymentsService.findAll(user);
  }

  /**
   * Get specific payment by ID
   */
  @ApiOperation({ summary: 'Get payment by ID' })
  @ApiOkResponse({
    description: 'Payment retrieved successfully',
    type: Payment,
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @Get(':id')
  async findOne(@Param() { id }: IdDto, @ActiveUser() user: IRequestUser) {
    return await this.paymentsService.findOne(id, user);
  }
}
</file>

<file path="src/modules/payments/payments.module.ts">
import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import paystackConfig from '../../core/config/payment.config';
import { Plan } from '../plans/entities/plan.entity';
import { User } from '../users/entities/user.entity';
import { Payment } from './entities/payment.entity';
import { PaymentsController } from './payments.controller';
import { PaymentsService } from './payments.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([Payment, User, Plan]),
    ConfigModule.forFeature(paystackConfig),
    HttpModule,
  ],
  controllers: [PaymentsController],
  providers: [PaymentsService],
  exports: [PaymentsService],
})
export class PaymentsModule {}
</file>

<file path="src/modules/plans/entities/plan.entity.ts">
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

import { RegistryDates } from 'src/core/common/dto/registry-dates.dto';

@Entity('plans')
export class Plan {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  slug: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'int', nullable: true })
  task_limit?: number | null; // null = unlimited

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  price: number;

  @Column({ type: 'boolean', default: false })
  is_subscription: boolean;

  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  @Column(() => RegistryDates, { prefix: false })
  public registry: RegistryDates;
}
</file>

<file path="src/modules/plans/plans.module.ts">
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { Plan } from './entities/plan.entity';
import { PlansController } from './plans.controller';
import { PlansService } from './plans.service';

@Module({
  imports: [TypeOrmModule.forFeature([Plan])],
  controllers: [PlansController],
  providers: [PlansService],
  exports: [PlansService],
})
export class PlansModule {}
</file>

<file path="src/modules/tasks/dto/create-task.dto.ts">
import {
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';

import { TaskStatus } from '../enums/task-status.enum';

export class CreateTaskDto {
  /**
   * Task title
   * @example "Complete project documentation"
   */
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  title: string;

  /**
   * Detailed description of the task
   * @example "Write comprehensive documentation for the API endpoints"
   */
  @IsString()
  @IsOptional()
  description?: string;

  /**
   * Task status
   * @example "todo"
   */
  @IsEnum(TaskStatus)
  @IsOptional()
  status?: TaskStatus;

  /**
   * Task due date
   * @example "2025-10-15T10:00:00Z"
   */
  @IsDateString()
  @IsOptional()
  due_at?: Date;
}
</file>

<file path="src/modules/tasks/dto/update-task.dto.ts">
import { PartialType } from '@nestjs/swagger';

import { CreateTaskDto } from './create-task.dto';

export class UpdateTaskDto extends PartialType(CreateTaskDto) {}
</file>

<file path="src/modules/tasks/tasks.controller.ts">
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import {
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { IdDto } from '../../core/common/dto/id.dto';
import { RemoveDto } from '../../core/common/dto/remove.dto';
import { ActiveUser } from '../auth/decorators/active-user.decorator';
import type { IRequestUser } from '../users/interfaces/user.interface';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { Task } from './entities/task.entity';
import { TasksService } from './tasks.service';

@Controller('tasks')
export class TasksController {
  constructor(private readonly tasksService: TasksService) {}

  @ApiOperation({ summary: 'Create a new task' })
  @ApiCreatedResponse({
    description: 'Task created successfully',
    type: Task,
  })
  @Post()
  async create(@ActiveUser() user: IRequestUser, @Body() dto: CreateTaskDto) {
    return await this.tasksService.create(user, dto);
  }

  @ApiOperation({ summary: 'Get all tasks - owner/admin' })
  @ApiOkResponse({
    description: 'List of tasks retrieved successfully',
    type: [Task],
  })
  @ApiUnauthorizedResponse({ description: 'Admin access required' })
  @Get()
  async findAll(@ActiveUser() user: IRequestUser) {
    return await this.tasksService.findAll(user);
  }

  @ApiOperation({ summary: 'Get task by ID - owner/admin' })
  @ApiOkResponse({
    description: 'Task retrieved successfully',
    type: Task,
  })
  @ApiUnauthorizedResponse({ description: 'Admin access required' })
  @ApiNotFoundResponse({ description: 'Task not found' })
  @Get(':id')
  async findOne(@Param() { id }: IdDto, @ActiveUser() user: IRequestUser) {
    return await this.tasksService.findOne(id, user);
  }

  @ApiOperation({ summary: 'Update task by ID - owner/admin' })
  @ApiOkResponse({
    description: 'Task updated successfully',
    type: Task,
  })
  @ApiUnauthorizedResponse({ description: 'Admin access required' })
  @ApiNotFoundResponse({ description: 'Task not found' })
  @Patch(':id')
  async update(
    @Param() { id }: IdDto,
    @ActiveUser() user: IRequestUser,
    @Body() dto: UpdateTaskDto,
  ) {
    return await this.tasksService.update(id, user, dto);
  }

  @ApiOperation({ summary: 'Soft/hard delete task by ID - owner/admin' })
  @ApiNoContentResponse({
    description: 'Task deleted successfully',
  })
  @ApiUnauthorizedResponse({ description: 'Admin access required' })
  @ApiNotFoundResponse({ description: 'Task not found' })
  @Delete(':id')
  async remove(
    @Param() { id }: IdDto,
    @ActiveUser() user: IRequestUser,
    @Query() { soft }: RemoveDto,
  ) {
    return await this.tasksService.remove(id, user, soft);
  }

  @ApiOperation({
    summary: 'Restore user task by ID',
  })
  @ApiNoContentResponse({
    description: 'Task restored successfully',
  })
  @ApiUnauthorizedResponse({ description: 'Admin access required' })
  @ApiNotFoundResponse({ description: 'Task not found' })
  @Get(':id/restore')
  async restore(@Param() { id }: IdDto, @ActiveUser() user: IRequestUser) {
    return await this.tasksService.restore(id, user);
  }
}
</file>

<file path="src/modules/tasks/tasks.module.ts">
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { User } from '../users/entities/user.entity';
import { Task } from './entities/task.entity';
import { TasksController } from './tasks.controller';
import { TasksService } from './tasks.service';

@Module({
  imports: [TypeOrmModule.forFeature([Task, User])],
  controllers: [TasksController],
  providers: [TasksService],
})
export class TasksModule {}
</file>

<file path="src/modules/users/users.module.ts">
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { User } from './entities/user.entity';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';

@Module({
  imports: [TypeOrmModule.forFeature([User])],
  controllers: [UsersController],
  providers: [UsersService],
})
export class UsersModule {}
</file>

<file path=".prettierrc">
{
  "singleQuote": true,
  "trailingComma": "all",
  "plugins": ["prettier-plugin-organize-imports"]
}
</file>

<file path="nest-cli.json">
{
  "$schema": "https://json.schemastore.org/nest-cli",
  "collection": "@nestjs/schematics",
  "sourceRoot": "src",
  "compilerOptions": {
    "deleteOutDir": true,
    "plugins": [
      {
        "name": "@nestjs/swagger",
        "options": {
          "dtoFileNameSuffix": [".dto.ts", ".entity.ts"],
          "introspectComments": true
        }
      }
    ]
  }
}
</file>

<file path="tsconfig.json">
{
  "compilerOptions": {
    "module": "nodenext",
    "moduleResolution": "nodenext",
    "resolvePackageJsonExports": true,
    "esModuleInterop": true,
    "isolatedModules": true,
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "target": "ES2023",
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./",
    "incremental": true,
    "skipLibCheck": true,
    "strictNullChecks": true,
    "forceConsistentCasingInFileNames": true,
    "noImplicitAny": false,
    "strictBindCallApply": false,
    "noFallthroughCasesInSwitch": false,
    "resolveJsonModule": true
  }
}
</file>

<file path="src/modules/auth/auth.service.ts">
import {
  BadRequestException,
  ConflictException,
  Inject,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import type { ConfigType } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { IPayload } from 'src/core/common/interfaces/payload.interface';
import emailConfig from 'src/core/config/email.config';
import jwtConfig from 'src/core/config/jwt.config';
import { RedisService } from 'src/core/redis/redis.service';

import { EmailService } from '../email/email.service';
import { PlansService } from '../plans/plans.service';
import { User } from '../users/entities/user.entity';
import { UserRole } from '../users/enums/roles.enum';
import type { IRequestUser } from '../users/interfaces/user.interface';
import { AuthDto } from './dto/auth.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { ResendVerificationDto } from './dto/resend-verification.dto';
import { VerifyEmailDto } from './dto/verify-email.dto';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private readonly usersRepo: Repository<User>,
    @Inject(jwtConfig.KEY)
    private readonly jwtCfg: ConfigType<typeof jwtConfig>,
    @Inject(emailConfig.KEY)
    private readonly emailCfg: ConfigType<typeof emailConfig>,
    private readonly jwtService: JwtService,
    private readonly redisService: RedisService,
    private readonly emailService: EmailService,
    private readonly plansService: PlansService,
  ) {}

  async validateLocal(dto: AuthDto) {
    const { email, password } = dto;
    const user = await this.usersRepo.findOne({
      where: { email },
      select: {
        id: true,
        email: true,
        password: true,
        verified: true,
        role: true,
      },
    });

    if (!user) {
      throw new UnauthorizedException('Invalid credential');
    }

    const isPasswordValid = await user.compare(password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credential');
    }

    if (!user.verified) {
      throw new UnauthorizedException(
        'Please verify your email before signing in',
      );
    }

    return this.createRequestUser(user);
  }

  async validateJwt(payload: IPayload) {
    const user = await this.usersRepo.findOneBy({ id: payload.sub });
    if (!user) throw new UnauthorizedException('Invalid token');

    return this.createRequestUser(user);
  }

  async signup(dto: AuthDto) {
    const { email, password } = dto;
    const existing = await this.usersRepo.findOneBy({ email });

    if (existing) throw new ConflictException('Account already exists!');

    // Get the default starter plan
    const starterPlan = await this.plansService.findOne('', 'starter');

    if (!starterPlan) {
      throw new Error(
        'Starter plan not found. Please ensure default plans are seeded.',
      );
    }

    const user = this.usersRepo.create({
      email,
      password,
      plan: starterPlan,
      tasks_left: starterPlan.task_limit,
    });
    const savedUser = await this.usersRepo.save(user);

    // Generate and send verification code
    await this.sendVerificationCode(savedUser);

    return {
      message: `A verification code has been sent to ${email}. Please check your email and verify your account at /api/v1/auth/verify-email`,
    };
  }

  async signin(user: IRequestUser) {
    return this.generateTokens(user);
  }

  async refreshTokens(userId: string, refreshToken: string) {
    const redisToken = await this.redisService.getRefreshToken(userId);
    if (!redisToken || redisToken !== refreshToken) {
      throw new UnauthorizedException('Invalid refresh token');
    }

    const decoded = this.jwtService.verify<IPayload>(refreshToken, {
      secret: this.jwtCfg.secret,
    });

    if (!decoded) {
      throw new UnauthorizedException('Invalid refresh token');
    }

    const payload: IRequestUser = {
      id: decoded.sub,
      email: decoded.email,
      role: decoded.role,
    };

    return this.generateTokens(payload, refreshToken);
  }

  async signout(userId: string) {
    await this.redisService.invalidateRefreshToken(userId);
  }

  async changePassword(id: string, dto: ChangePasswordDto) {
    const { newPassword, currentPassword } = dto;
    const user = await this.usersRepo.findOneBy({ id });
    if (!user) throw new NotFoundException('User not found!');

    if (!(await user.compare(currentPassword)))
      throw new UnauthorizedException('Invalid current password!');

    user.password = newPassword;
    await this.usersRepo.save(user);

    // Invalidate refresh token after password change
    await this.redisService.invalidateRefreshToken(id);

    return { message: 'Password changed successfully' };
  }

  async assignRole(id: string, role: UserRole) {
    const user = await this.usersRepo.preload({ id, role });

    if (!user) throw new NotFoundException('User not found');

    return this.usersRepo.save(user);
  }

  createRequestUser(user: User): IRequestUser {
    const { id, email, role } = user;
    return { id, email, role };
  }

  private async signToken(
    payload: IPayload,
    secret: string,
    expiresIn: number,
  ) {
    return this.jwtService.signAsync(payload, { secret, expiresIn });
  }

  async generateTokens(user: IRequestUser, oldRefreshToken?: string) {
    // Invalidate old refresh token if provided
    if (oldRefreshToken) {
      await this.redisService.invalidateRefreshToken(user.id);
    }

    const payload: IPayload = {
      sub: user.id,
      email: user.email,
      role: user.role,
    };

    const accessToken = await this.signToken(
      payload,
      this.jwtCfg.secret,
      this.jwtCfg.signOptions.expiresIn,
    );

    const refreshToken = await this.signToken(
      payload,
      this.jwtCfg.secret,
      this.jwtCfg.refreshTokenTtl,
    );

    await this.redisService.setRefreshToken(
      user.id,
      refreshToken,
      this.jwtCfg.refreshTokenTtl,
    );

    return { accessToken, refreshToken };
  }

  async verifyEmail(dto: VerifyEmailDto) {
    const { email, code } = dto;
    const user = await this.usersRepo.findOne({
      where: { email },
      select: {
        id: true,
        email: true,
        verified: true,
        verification_code: true,
        verification_code_expires_at: true,
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (user.verified) {
      throw new BadRequestException('Email already verified');
    }

    if (!user.verification_code || !user.verification_code_expires_at) {
      throw new BadRequestException(
        'No verification code found. Please request a new one',
      );
    }

    if (new Date() > user.verification_code_expires_at) {
      throw new BadRequestException(
        'Verification code has expired. Please request a new one',
      );
    }

    if (user.verification_code !== code) {
      throw new BadRequestException('Invalid verification code');
    }

    // Mark user as verified
    user.verified = true;
    user.verification_code = undefined;
    user.verification_code_expires_at = undefined;
    await this.usersRepo.save(user);

    return {
      message:
        'Email verified successfully. You can now sign in at /api/v1/auth/signin',
    };
  }

  async resendVerificationCode(dto: ResendVerificationDto) {
    const { email } = dto;
    const user = await this.usersRepo.findOne({ where: { email } });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (user.verified) {
      throw new BadRequestException('Email already verified');
    }

    await this.sendVerificationCode(user);

    return {
      message: 'Verification code sent successfully. Please check your email.',
    };
  }

  private async sendVerificationCode(user: User): Promise<void> {
    const code = this.generateVerificationCode();
    const expiresAt = new Date(Date.now() + this.emailCfg.verificationCodeTtl);

    user.verification_code = code;
    user.verification_code_expires_at = expiresAt;
    await this.usersRepo.save(user);

    await this.emailService.sendVerificationEmail(user.email, code, user.name);
  }

  private generateVerificationCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }
}
</file>

<file path="src/modules/tasks/entities/task.entity.ts">
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

import { RegistryDates } from 'src/core/common/dto/registry-dates.dto';
import { User } from 'src/modules/users/entities/user.entity';
import { TaskStatus } from '../enums/task-status.enum';

@Entity('tasks')
export class Task {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({
    type: 'enum',
    enum: TaskStatus,
    enumName: 'task_status',
    default: TaskStatus.TODO,
  })
  status: TaskStatus;

  @Column({ type: 'timestamp', nullable: true })
  due_at?: Date;

  @ManyToOne(() => User, { onDelete: 'CASCADE', eager: false })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column(() => RegistryDates, { prefix: false })
  public registry: RegistryDates;

  get isDeleted(): boolean {
    return !!this.registry.deletedAt;
  }
}
</file>

<file path="src/modules/tasks/tasks.service.ts">
import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { compareIds } from '../../core/common/utils/compare-ids.util';
import { User } from '../users/entities/user.entity';
import { UserRole } from '../users/enums/roles.enum';
import { IRequestUser } from '../users/interfaces/user.interface';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { Task } from './entities/task.entity';

@Injectable()
export class TasksService {
  constructor(
    @InjectRepository(Task) private readonly tasksRepo: Repository<Task>,
    @InjectRepository(User) private readonly usersRepo: Repository<User>,
  ) {}

  public async create(currentUser: IRequestUser, dto: CreateTaskDto) {
    const user = await this.usersRepo.findOneOrFail({
      where: { id: currentUser.id },
    });

    // Check if user has tasks left (null = unlimited)
    if (user.tasks_left !== null && user.tasks_left <= 0) {
      throw new BadRequestException(
        'No tasks left. Please upgrade your plan to create more tasks.',
      );
    }

    // Decrement tasks_left only if not unlimited
    if (user.tasks_left !== null) {
      user.tasks_left -= 1;
      await this.usersRepo.save(user);
    }

    const task = this.tasksRepo.create({
      ...dto,
      user,
    });

    return await this.tasksRepo.save(task);
  }

  public async findAll(currentUser: IRequestUser) {
    // Admins can see all tasks, users see only their own
    const where =
      currentUser.role === UserRole.ADMIN
        ? {}
        : { user: { id: currentUser.id } };

    return await this.tasksRepo.find({
      where,
      relations: { user: true },
      order: { registry: { createdAt: 'DESC' } },
    });
  }

  public async findOne(id: string, currentUser: IRequestUser) {
    const task = await this.tasksRepo.findOneOrFail({
      where: { id },
      relations: { user: true },
    });

    // Check if user owns the task or is admin
    if (currentUser.role !== UserRole.ADMIN) {
      compareIds(currentUser.id, task.user.id);
    }

    return task;
  }

  public async update(
    id: string,
    currentUser: IRequestUser,
    dto: UpdateTaskDto,
  ) {
    // First get the task to check ownership
    const existingTask = await this.tasksRepo.findOneOrFail({
      where: { id },
      relations: { user: true },
    });

    // Check if user owns the task or is admin
    if (currentUser.role !== UserRole.ADMIN) {
      compareIds(currentUser.id, existingTask.user.id);
    }

    const task = await this.tasksRepo.preload({
      id,
      ...dto,
    });

    if (!task) throw new NotFoundException('Task not found');
    return await this.tasksRepo.save(task);
  }

  public async remove(
    id: string,
    currentUser: IRequestUser,
    soft: boolean = false,
  ) {
    const task = await this.findOne(id, currentUser);

    // Authorization is already checked in findOne method
    // No need to check again here

    if (!soft) throw new ForbiddenException('Forbidden resource');

    return soft
      ? await this.tasksRepo.softRemove(task)
      : await this.tasksRepo.remove(task);
  }

  public async restore(id: string, currentUser: IRequestUser) {
    const task = await this.tasksRepo.findOne({
      where: { id },
      relations: { user: true },
      withDeleted: true,
    });

    if (!task) throw new NotFoundException('Task not found');

    // Check if user owns the task or is admin
    if (currentUser.role !== UserRole.ADMIN) {
      compareIds(currentUser.id, task.user.id);
    }

    if (!task.isDeleted) throw new ConflictException('Task not deleted');
    return await this.tasksRepo.recover(task);
  }
}
</file>

<file path="src/modules/users/users.controller.ts">
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import {
  ApiConflictResponse,
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { IdDto } from '../../core/common/dto/id.dto';
import { RemoveDto } from '../../core/common/dto/remove.dto';
import { ActiveUser } from '../auth/decorators/active-user.decorator';
import { Public } from '../auth/decorators/public.decorator';
import { Roles } from '../auth/decorators/roles.decorator';
import { AuthDto } from '../auth/dto/auth.dto';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { User } from './entities/user.entity';
import { UserRole } from './enums/roles.enum';
import type { IRequestUser } from './interfaces/user.interface';
import { UsersService } from './users.service';

@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @ApiOperation({ summary: 'Create a new user account - admin only' })
  @ApiCreatedResponse({
    description: 'User account created successfully',
    type: User,
  })
  @ApiConflictResponse({ description: 'User with this email already exists' })
  @ApiUnauthorizedResponse({ description: 'Admin access required' })
  @Post()
  @Roles(UserRole.ADMIN)
  async create(@Body() dto: CreateUserDto): Promise<User> {
    return await this.usersService.create(dto);
  }

  @ApiOperation({ summary: 'Get all user accounts - admin only' })
  @ApiOkResponse({
    description: 'List of all user accounts retrieved successfully',
    type: [User],
  })
  @ApiUnauthorizedResponse({ description: 'Admin access required' })
  @Get()
  @Roles(UserRole.ADMIN)
  async findAll(): Promise<User[]> {
    return this.usersService.findAll();
  }

  @ApiOperation({ summary: 'Get user account by ID - owner/admin' })
  @ApiOkResponse({
    description: 'User account retrieved successfully',
    type: User,
  })
  @ApiUnauthorizedResponse({ description: 'Admin access required' })
  @ApiNotFoundResponse({ description: 'User not found' })
  @Get(':id')
  async findOne(@Param() { id }: IdDto, @ActiveUser() user: IRequestUser) {
    return await this.usersService.findOne(id, user);
  }

  @ApiOperation({ summary: 'Update user profile - owner/admin' })
  @ApiOkResponse({
    description: 'User profile updated successfully',
    type: User,
  })
  @ApiUnauthorizedResponse({ description: 'Admin access required' })
  @ApiNotFoundResponse({ description: 'User not found' })
  @Patch(':id')
  updateProfile(
    @Param() { id }: IdDto,
    @ActiveUser() user: IRequestUser,
    @Body() dto: UpdateUserDto,
  ): Promise<User> {
    return this.usersService.update(id, user, dto);
  }

  @ApiOperation({
    summary: 'Deactivate/Delete user account by ID - owner/admin',
  })
  @ApiNoContentResponse({
    description: 'User account deactivated/deleted successfully',
  })
  @ApiUnauthorizedResponse({ description: 'Admin access required' })
  @ApiNotFoundResponse({ description: 'User not found' })
  @Delete(':id')
  remove(
    @Param() { id }: IdDto,
    @ActiveUser() user: IRequestUser,
    @Query() { soft }: RemoveDto,
  ): Promise<User> {
    return this.usersService.remove(id, user, soft);
  }

  @ApiOperation({ summary: 'Recover user account by email' })
  @ApiOkResponse({ description: 'Account recovered if exists' })
  @ApiConflictResponse({
    description: 'User with this email was not deleted',
  })
  @Get('recover')
  @Public()
  recover(@Query() dto: AuthDto): Promise<User> {
    return this.usersService.recover(dto);
  }
}
</file>

<file path="src/modules/users/users.service.ts">
import {
  ConflictException,
  ForbiddenException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { compareIds } from '../../core/common/utils/compare-ids.util';
import { AuthDto } from '../auth/dto/auth.dto';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { User } from './entities/user.entity';
import { UserRole } from './enums/roles.enum';
import { IRequestUser } from './interfaces/user.interface';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User) private readonly usersRepo: Repository<User>,
  ) {}

  public async create(dto: CreateUserDto): Promise<User> {
    const existingUser = await this.usersRepo.findOne({
      where: { email: dto.email },
    });

    if (existingUser) throw new ConflictException('Email already exists');

    const user = this.usersRepo.create(dto);
    return await this.usersRepo.save(user);
  }

  public async findAll(): Promise<User[]> {
    return await this.usersRepo.find({
      relations: { plan: true },
      order: { registry: { createdAt: 'DESC' } },
    });
  }

  public async findOne(id: string, currentUser: IRequestUser): Promise<User> {
    if (currentUser.role !== UserRole.ADMIN) compareIds(currentUser.id, id);

    return await this.usersRepo.findOneOrFail({
      where: { id },
      relations: { plan: true },
    });
  }

  public async update(
    id: string,
    currentUser: IRequestUser,
    dto: UpdateUserDto,
  ): Promise<User> {
    if (currentUser.role !== UserRole.ADMIN) compareIds(currentUser.id, id);

    const user = await this.usersRepo.preload({
      id,
      name: dto.name,
      phone: dto.phone,
      bio: dto.bio,
    });

    if (!user) throw new NotFoundException('User not found');
    return this.usersRepo.save(user);
  }

  public async remove(
    id: string,
    currentUser: IRequestUser,
    soft: boolean = false,
  ): Promise<User> {
    if (currentUser.role !== UserRole.ADMIN) compareIds(currentUser.id, id);
    if (!soft) throw new ForbiddenException('Forbidden resource');

    const user = await this.findOne(id, currentUser);
    return soft ? this.usersRepo.softRemove(user) : this.usersRepo.remove(user);
  }

  public async recover(dto: AuthDto): Promise<User> {
    const { email, password } = dto;

    const user = await this.usersRepo.findOne({
      where: { email },
      withDeleted: true,
    });

    if (!user || !(await user.compare(password)))
      throw new UnauthorizedException('Invalid credentials!');

    if (!user.isDeleted) throw new ConflictException('User not deleted');
    return await this.usersRepo.recover(user);
  }

  /**
   * Update user's task limits (used by payment service)
   */
  public async updateUserTaskLimits(
    userId: string,
    additionalTasks?: number | null,
  ): Promise<User> {
    const user = await this.usersRepo.findOne({
      where: { id: userId },
      relations: { plan: true },
    });

    if (!user) throw new NotFoundException('User not found');

    // If switching to unlimited plan (Flow)
    if (additionalTasks === null) {
      user.tasks_left = null;
    } else if (additionalTasks !== undefined) {
      // Adding tasks (Focus plan)
      user.tasks_left = (user.tasks_left || 0) + additionalTasks;
    }

    return await this.usersRepo.save(user);
  }
}
</file>

<file path="src/app.module.ts">
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';

import { AuthModule } from './modules/auth/auth.module';
import { EmailModule } from './modules/email/email.module';
import { HealthModule } from './modules/health/health.module';
import { PaymentsModule } from './modules/payments/payments.module';
import { PlansModule } from './modules/plans/plans.module';
import { TasksModule } from './modules/tasks/tasks.module';
import { UsersModule } from './modules/users/users.module';

import { validateEnv } from './core/config/app.config';
import { apiProviders } from './core/config/providers.config';
import throttlerConfig from './core/config/throttler.config';
import { DbModule } from './core/db/db.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      expandVariables: true,
      validate: validateEnv,
    }),
    ThrottlerModule.forRootAsync(throttlerConfig.asProvider()),
    DbModule,
    AuthModule,
    EmailModule,
    HealthModule,
    PaymentsModule,
    PlansModule,
    TasksModule,
    UsersModule,
  ],
  controllers: [],
  providers: [...apiProviders],
})
export class AppModule {}
</file>

<file path=".env.example">
# General
NODE_ENV=production
API_PORT=5155
API_TOKEN="API_CONFIG"
TZ=Africa/Lagos
API_URL="http://aiki-api:5155"

PUID=1000
PGID=1000

# Database
DB_URL=pg:5432/aiki
REDIS_HOST=redis
REDIS_PORT=6379

# NOTE: All TTLs are in seconds
# JWT
JWT_SECRET="64-character-secret-key"
JWT_ISSUER="http://aiki-api:5155"
JWT_AUDIENCE="http://aiki-api:5155"
JWT_ACCESS_TOKEN_TTL=900 # 15 minutes
JWT_REFRESH_TOKEN_TTL=604800 # 7 days

# THROTTLER
THROTTLER_TTL=60
THROTTLER_LIMIT=10

# HTTP (axios)
HTTP_TIMEOUT=5000
HTTP_MAX_REDIRECTS=5

# Email
VERIFICATION_CODE_TTL=600 # 10 minutes (for account verification)
EMAIL_FROM='Aiki Team'
EMAIL_SENDER=''
EMAIL_HOST="smtp.zoho.com"
EMAIL_PORT=465
EMAIL_USERNAME=""
EMAIL_PASSWORD=""
EMAIL_SECURE=true

# Paystack
PAYSTACK_SECRET_KEY=""
PAYSTACK_PUBLIC_KEY=""
PAYSTACK_BASE_URL="https://api.paystack.co"
PAYSTACK_CALLBACK_URL="https://aiki.dovely.tech/api/v1/payments/callback"
PAYSTACK_WEBHOOK_URL="https://aiki.dovely.tech/api/v1/payments/webhook"
FLOW_PLAN_CODE=""
</file>

<file path="compose.yml">
name: aiki
services:
  aiki-api:
    container_name: "aiki-api"
    image: ghcr.io/claudiusayadi/aiki-api:latest
    restart: unless-stopped
    security_opt:
      - no-new-privileges
    ports:
      - 127.0.0.1:$API_PORT:$API_PORT
    environment:
      NODE_ENV: $NODE_ENV
      API_PORT: $API_PORT
      API_TOKEN: $API_TOKEN
      TZ: $TZ
      API_URL: $API_URL
      PUID: $PUID
      PGID: $PGID
      # Database
      DB_URL: $DB_URL
      REDIS_HOST: $REDIS_HOST
      REDIS_PORT: $REDIS_PORT
      # NOTE: All TTLs are in seconds
      # JWT
      JWT_SECRET: $JWT_SECRET
      JWT_ISSUER: $JWT_ISSUER
      JWT_AUDIENCE: $JWT_AUDIENCE
      JWT_ACCESS_TOKEN_TTL: $JWT_ACCESS_TOKEN_TTL
      JWT_REFRESH_TOKEN_TTL: $JWT_REFRESH_TOKEN_TTL
      # THROTTLER
      THROTTLER_TTL: $THROTTLER_TTL
      THROTTLER_LIMIT: $THROTTLER_LIMIT
      # HTTP (axios)
      HTTP_TIMEOUT: $HTTP_TIMEOUT
      HTTP_MAX_REDIRECTS: $HTTP_MAX_REDIRECTS
      # Email
      EMAIL_FROM: $EMAIL_FROM
      EMAIL_SENDER: $EMAIL_SENDER
      EMAIL_HOST: $EMAIL_HOST
      EMAIL_PORT: $EMAIL_PORT
      EMAIL_USERNAME: $EMAIL_USERNAME
      EMAIL_PASSWORD: $EMAIL_PASSWORD
      EMAIL_SECURE: $EMAIL_SECURE
      VERIFICATION_CODE_TTL: $VERIFICATION_CODE_TTL

      # Paystack
      PAYSTACK_SECRET_KEY: $PAYSTACK_SECRET_KEY
      PAYSTACK_PUBLIC_KEY: $PAYSTACK_PUBLIC_KEY
      PAYSTACK_BASE_URL: $PAYSTACK_BASE_URL
      PAYSTACK_CALLBACK_URL: $PAYSTACK_CALLBACK_URL
      PAYSTACK_WEBHOOK_URL: $PAYSTACK_WEBHOOK_URL
      FLOW_PLAN_CODE: $FLOW_PLAN_CODE
    networks:
      - pg
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:$API_PORT/health"]
      interval: 10s
      timeout: 5s
      retries: 5

networks:
  pg:
    external: true
  redis:
    external: true
</file>

<file path="src/modules/plans/plans.controller.ts">
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
} from '@nestjs/common';
import {
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiOperation,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { DeleteResult, UpdateResult } from 'typeorm';

import { IdDto } from '../../core/common/dto/id.dto';
import { Public } from '../auth/decorators/public.decorator';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/enums/roles.enum';
import { CreatePlanDto } from './dto/create-plan.dto';
import { UpdatePlanDto } from './dto/update-plan.dto';
import { Plan } from './entities/plan.entity';
import { PlansService } from './plans.service';

@Controller('plans')
export class PlansController {
  constructor(private readonly plansService: PlansService) {}

  @ApiOperation({ summary: 'Create a new plan' })
  @ApiCreatedResponse({ type: Plan })
  @ApiUnauthorizedResponse({ description: 'Admin access required' })
  @Roles(UserRole.ADMIN)
  @Post()
  create(@Body() dto: CreatePlanDto): Promise<Plan> {
    return this.plansService.create(dto);
  }

  @ApiOperation({ summary: 'Get all plans' })
  @ApiOkResponse({ type: [Plan] })
  @Public()
  @Get()
  findAll(): Promise<Plan[]> {
    return this.plansService.findAll();
  }

  @ApiOperation({ summary: 'Get a plan by ID' })
  @ApiOkResponse({ type: Plan })
  @Public()
  @Get(':id')
  findOne(@Param() { id }: IdDto): Promise<Plan> {
    return this.plansService.findOne(id);
  }

  @ApiOperation({ summary: 'Update a plan' })
  @ApiOkResponse({ type: Plan })
  @ApiUnauthorizedResponse({ description: 'Admin access required' })
  @Roles(UserRole.ADMIN)
  @Patch(':id')
  update(
    @Param() { id }: IdDto,
    @Body() dto: UpdatePlanDto,
  ): Promise<UpdateResult> {
    return this.plansService.update(id, dto);
  }

  @ApiOperation({ summary: 'Delete a plan' })
  @ApiNoContentResponse({ description: 'Plan deleted successfully' })
  @ApiUnauthorizedResponse({ description: 'Admin access required' })
  @Roles(UserRole.ADMIN)
  @Delete(':id')
  remove(@Param() { id }: IdDto): Promise<DeleteResult> {
    return this.plansService.remove(id);
  }
}
</file>

<file path="src/modules/plans/plans.service.ts">
import { Injectable, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeleteResult, Repository, UpdateResult } from 'typeorm';

import { ApiConfig } from '../../core/config/app.config';
import { CreatePlanDto } from '../plans/dto/create-plan.dto';
import { UpdatePlanDto } from '../plans/dto/update-plan.dto';
import { Plan } from './entities/plan.entity';

@Injectable()
export class PlansService implements OnModuleInit {
  constructor(
    @InjectRepository(Plan)
    private readonly planRepo: Repository<Plan>,
  ) {}

  async onModuleInit() {
    await this.seedDefaultPlans();
  }

  create(dto: CreatePlanDto): Promise<Plan> {
    return this.planRepo.save(dto);
  }

  findAll(): Promise<Plan[]> {
    return this.planRepo.find();
  }

  findOne(id: string, slug?: string): Promise<Plan> {
    return this.planRepo.findOneOrFail({
      where: slug ? { slug } : { id },
    });
  }

  update(id: string, dto: UpdatePlanDto): Promise<UpdateResult> {
    return this.planRepo.update(id, dto);
  }

  remove(id: string): Promise<DeleteResult> {
    return this.planRepo.delete(id);
  }

  private async seedDefaultPlans() {
    const count = await this.planRepo.count();
    if (count > 0) return;

    const plans = [
      {
        slug: 'starter',
        name: 'Starter',
        description: 'Free forever. Create up to 5 tasks to get started.',
        task_limit: 5,
        price: 0,
        is_subscription: false,
      },
      {
        slug: 'focus',
        name: 'Focus',
        description:
          'Pay-as-you-go plan. Purchase task slots as you need them.',
        task_limit: null,
        price: 1000,
        is_subscription: false,
      },
      {
        slug: 'flow',
        name: 'Flow',
        description:
          'Unlimited tasks with a monthly subscription. Renewed automatically.',
        task_limit: null,
        price: 10000,
        is_subscription: true,
        metadata: {
          paystack_plan_code: ApiConfig.FLOW_PLAN_CODE,
        },
      },
    ];

    await this.planRepo.save(plans);
    console.log('Default plans seeded.');
  }
}
</file>

<file path="src/modules/users/entities/user.entity.ts">
import * as argon from 'argon2';
import { Exclude } from 'class-transformer';
import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

import { RegistryDates } from 'src/core/common/dto/registry-dates.dto';
import { Plan } from 'src/modules/plans/entities/plan.entity';
import { UserRole } from '../enums/roles.enum';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  email: string;

  @Exclude()
  @Column({ type: 'varchar', length: 255 })
  password: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  name?: string;

  @Column({ type: 'varchar', length: 20, unique: true, nullable: true })
  phone?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  bio?: string;

  @Column({ type: 'boolean', default: false })
  verified: boolean;

  @Exclude()
  @Column({ type: 'varchar', length: 6, nullable: true })
  verification_code?: string;

  @Exclude()
  @Column({ type: 'timestamp', nullable: true })
  verification_code_expires_at?: Date;

  @ManyToOne(() => Plan, { eager: false })
  @JoinColumn({ name: 'plan_id' })
  plan: Plan;

  @Column({ type: 'int', nullable: true, default: 5 })
  tasks_left: number | null;

  @Column({ type: 'timestamp', nullable: true })
  renews_at: Date | null;

  @Column({
    type: 'enum',
    enum: UserRole,
    enumName: 'user_role',
    default: UserRole.USER,
  })
  role: UserRole;

  @Column({ type: 'timestamp', name: 'last_login_at', nullable: true })
  lastLoginAt?: Date;

  @Column(() => RegistryDates, { prefix: false })
  registry: RegistryDates;

  @BeforeInsert()
  @BeforeUpdate()
  protected async hashPassword() {
    if (this.password) {
      // Only hash if it's not already hashed
      if (!this.password.startsWith('$argon2')) {
        this.password = await argon.hash(this.password);
      }
    }
  }

  async compare(password: string): Promise<boolean> {
    if (!this.password) throw new Error('User password hash is missing');
    if (!password) throw new Error('Password to compare is missing');
    return await argon.verify(this.password, password);
  }

  get isDeleted(): boolean {
    return !!this.registry.deletedAt;
  }
}
</file>

<file path="package.json">
{
  "name": "aiki",
  "version": "1.0.0",
  "description": "Aiki is a modern task management API built with NestJS, empowering users to organize, prioritize, and supercharge productivity through flexible plans - from free to pay-as-you-go to subscription tiers.",
  "author": "",
  "private": true,
  "license": "UNLICENSED",
  "scripts": {
    "build": "nest build",
    "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"",
    "start": "nest start",
    "start:dev": "nest start --watch",
    "start:debug": "nest start --debug --watch",
    "start:prod": "node dist/main",
    "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:cov": "jest --coverage",
    "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand",
    "test:e2e": "jest --config ./test/jest-e2e.json",
    "premigration:generate": "yarn run build",
    "migration:generate": "yarn typeorm migration:generate -d dist/src/core/config/data-source -p",
    "premigration:run": "yarn run build",
    "migration:run": "yarn typeorm migration:run -d dist/src/core/config/data-source",
    "migration:revert": "yarn typeorm migration:revert -d dist/src/core/config/data-source"
  },
  "dependencies": {
    "@nestjs-modules/mailer": "^2.0.2",
    "@nestjs/axios": "^4.0.1",
    "@nestjs/common": "^11.0.1",
    "@nestjs/config": "^4.0.2",
    "@nestjs/core": "^11.0.1",
    "@nestjs/jwt": "^11.0.0",
    "@nestjs/passport": "^11.0.5",
    "@nestjs/platform-express": "^11.0.1",
    "@nestjs/swagger": "^11.2.0",
    "@nestjs/terminus": "^11.0.0",
    "@nestjs/throttler": "^6.4.0",
    "@nestjs/typeorm": "^11.0.0",
    "argon2": "^0.44.0",
    "axios": "^1.12.2",
    "class-transformer": "^0.5.1",
    "class-validator": "^0.14.2",
    "cookie-parser": "^1.4.7",
    "handlebars": "^4.7.8",
    "helmet": "^8.1.0",
    "ioredis": "^5.8.1",
    "nodemailer": "^7.0.9",
    "passport": "^0.7.0",
    "passport-jwt": "^4.0.1",
    "passport-local": "^1.0.0",
    "pg": "^8.16.3",
    "reflect-metadata": "^0.2.2",
    "rxjs": "^7.8.1",
    "typeorm": "^0.3.27"
  },
  "devDependencies": {
    "@eslint/eslintrc": "^3.2.0",
    "@eslint/js": "^9.18.0",
    "@nestjs/cli": "^11.0.0",
    "@nestjs/schematics": "^11.0.0",
    "@nestjs/testing": "^11.0.1",
    "@types/cookie-parser": "^1.4.9",
    "@types/express": "^5.0.0",
    "@types/jest": "^30.0.0",
    "@types/node": "^22.10.7",
    "@types/nodemailer": "^7.0.2",
    "@types/passport-jwt": "^4.0.1",
    "@types/passport-local": "^1.0.38",
    "@types/supertest": "^6.0.2",
    "eslint": "^9.18.0",
    "eslint-config-prettier": "^10.0.1",
    "eslint-plugin-prettier": "^5.2.2",
    "globals": "^16.0.0",
    "jest": "^30.0.0",
    "prettier": "^3.4.2",
    "prettier-plugin-organize-imports": "^4.3.0",
    "source-map-support": "^0.5.21",
    "supertest": "^7.0.0",
    "ts-jest": "^29.2.5",
    "ts-loader": "^9.5.2",
    "ts-node": "^10.9.2",
    "tsconfig-paths": "^4.2.0",
    "typescript": "^5.7.3",
    "typescript-eslint": "^8.20.0",
    "zod": "^4.1.11"
  },
  "jest": {
    "moduleFileExtensions": [
      "js",
      "json",
      "ts"
    ],
    "rootDir": "src",
    "testRegex": ".*\\.spec\\.ts$",
    "transform": {
      "^.+\\.(t|j)s$": "ts-jest"
    },
    "collectCoverageFrom": [
      "**/*.(t|j)s"
    ],
    "coverageDirectory": "../coverage",
    "testEnvironment": "node"
  }
}
</file>

<file path=".github/workflows/api.yml">
name: API CI/CD

on:
  push:
    branches: [main]

  pull_request:
    branches: [main]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}-api

jobs:
  build-and-push:
    name: Build and Push API Docker Image
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Extract version from package.json
        id: version
        run: echo "VERSION=$(jq -r '.version' package.json)" >> $GITHUB_OUTPUT

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=raw,value=${{ steps.version.outputs.VERSION }}
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push API Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64
</file>

<file path="src/core/config/app.config.ts">
import * as dotenv from 'dotenv';
import * as dotenvExpand from 'dotenv-expand';
import { z } from 'zod';

dotenvExpand.expand(dotenv.config());

export const envSchema = z.object({
  NODE_ENV: z
    .enum(['development', 'production', 'test', 'staging'])
    .default('development'),
  API_PORT: z.coerce.number().min(1, 'API_PORT is required!'),
  API_TOKEN: z.string().min(1, 'API_TOKEN is required!'),

  DB_URL: z.string().min(1, 'DB_URL is required!'),
  REDIS_HOST: z.string().optional(),
  REDIS_PORT: z.coerce.number().optional(),

  JWT_SECRET: z.string().min(64, 'JWT_SECRET must be at least 64 characters!'),
  JWT_ISSUER: z.string().min(1, 'JWT_ISSUER is required!'),
  JWT_AUDIENCE: z.string().min(1, 'JWT_AUDIENCE is required!'),
  JWT_ACCESS_TOKEN_TTL: z.coerce
    .number()
    .min(1, 'JWT_ACCESS_TOKEN_TTL is required!'),
  JWT_REFRESH_TOKEN_TTL: z.coerce
    .number()
    .min(1, 'JWT_REFRESH_TOKEN_TTL is required!'),

  HTTP_TIMEOUT: z.coerce.number().min(1, 'HTTP_TTL is required!'),
  HTTP_MAX_REDIRECTS: z.coerce
    .number()
    .min(0, 'HTTP_MAX_REDIRECTS is required!'),

  THROTTLER_TTL: z.coerce.number().min(1, 'THROTTLER_TTL is required!'),
  THROTTLER_LIMIT: z.coerce.number().min(1, 'THROTTLER_LIMIT is required!'),

  VERIFICATION_CODE_TTL: z.coerce
    .number()
    .min(1, 'VERIFICATION_CODE_EXPIRY is required!'),
  EMAIL_HOST: z.string().min(1, 'EMAIL_HOST is required!'),
  EMAIL_PORT: z.coerce.number().min(1, 'EMAIL_PORT is required!'),
  EMAIL_USERNAME: z.string().min(1, 'EMAIL_USERNAME is required!'),
  EMAIL_PASSWORD: z.string().min(1, 'EMAIL_PASSWORD is required!'),
  EMAIL_FROM: z.string().min(1, 'EMAIL_FROM is required!'),
  EMAIL_SENDER: z.string().min(1, 'EMAIL_SENDER is required!'),
  EMAIL_SECURE: z.coerce.boolean(),

  PAYSTACK_SECRET_KEY: z.string().min(1, 'PAYSTACK_SECRET_KEY is required!'),
  PAYSTACK_PUBLIC_KEY: z.string().min(1, 'PAYSTACK_PUBLIC_KEY is required!'),
  PAYSTACK_BASE_URL: z.string().min(1, 'PAYSTACK_BASE_URL is required!'),
  PAYSTACK_CALLBACK_URL: z
    .string()
    .min(1, 'PAYSTACK_CALLBACK_URL is required!'),
  PAYSTACK_WEBHOOK_URL: z.string().min(1, 'PAYSTACK_WEBHOOK_URL is required!'),
  FLOW_PLAN_CODE: z.string().min(1, 'FLOW_PLAN_CODE is required!'),
});

export type ApiConfig = z.infer<typeof envSchema>;

export const validateEnv = (): ApiConfig => envSchema.parse(process.env);

export const ApiConfig = validateEnv();
</file>

<file path="src/modules/payments/payments.service.ts">
import { HttpService } from '@nestjs/axios';
import {
  BadRequestException,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import type { ConfigType } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import * as crypto from 'crypto';
import { firstValueFrom } from 'rxjs';
import { Repository } from 'typeorm';

import paymentConfig from '../../core/config/payment.config';
import { Plan } from '../plans/entities/plan.entity';
import { User } from '../users/entities/user.entity';
import type { IRequestUser } from '../users/interfaces/user.interface';
import { InitializePaymentDto } from './dto/initialize-payment.dto';
import { VerifyPaymentDto } from './dto/verify-payment.dto';
import { Payment } from './entities/payment.entity';
import { PaymentStatus } from './enums/payment-status.enum';
import { PaymentType } from './enums/payment-type.enum';
import type { IPaymentInitializeResponse } from './interfaces/initialize-response.interface';
import type { IPaymentSubscriptionResponse } from './interfaces/subscription-response.interface';
import type { IPaymentVerificationResponse } from './interfaces/verify-response.interface';

@Injectable()
export class PaymentsService {
  private readonly logger = new Logger(PaymentsService.name);

  constructor(
    @InjectRepository(Payment)
    private readonly paymentsRepo: Repository<Payment>,
    @InjectRepository(User) private readonly usersRepo: Repository<User>,
    @InjectRepository(Plan) private readonly plansRepo: Repository<Plan>,
    @Inject(paymentConfig.KEY)
    private readonly config: ConfigType<typeof paymentConfig>,
    private readonly httpService: HttpService,
  ) {}

  public async initializePayment(
    currentUser: IRequestUser,
    dto: InitializePaymentDto,
  ) {
    const plan = await this.plansRepo.findOne({ where: { id: dto.plan_id } });
    if (!plan) throw new NotFoundException('Plan not found');

    const user = await this.usersRepo.findOne({
      where: { id: currentUser.id },
    });
    if (!user) throw new NotFoundException('User not found');

    // One-time payment (focus plan)
    if (!plan.is_subscription) {
      const quantity = dto.quantity || 1;
      const amount = Number(plan.price) * quantity * 100; // In kobo

      // Generate unique reference
      const reference = `aiki-${plan.slug}-${user.id}-${Date.now()}`;

      try {
        // Initialize transaction with Paystack
        const response = await firstValueFrom(
          this.httpService.post<IPaymentInitializeResponse>(
            `${this.config.baseUrl}/transaction/initialize`,
            {
              email: user.email,
              amount: amount.toString(),
              reference,
              callback_url: this.config.callbackUrl,
              metadata: {
                plan_id: plan.id,
                plan_name: plan.name,
                quantity,
                user_id: user.id,
              },
            },
            {
              headers: {
                Authorization: `Bearer ${this.config.secretKey}`,
                'Content-Type': 'application/json',
              },
            },
          ),
        );

        // Save payment record
        const payment = this.paymentsRepo.create({
          transaction_reference: reference,
          amount: Number(plan.price) * quantity,
          currency: 'NGN',
          status: PaymentStatus.PENDING,
          payment_type: PaymentType.ONE_TIME,
          quantity,
          user: { id: user.id },
          plan: { id: plan.id },
          metadata: {
            paystack_access_code: response.data.data.access_code,
          },
        });

        await this.paymentsRepo.save(payment);

        return {
          access_code: response.data.data.access_code,
          authorization_url: response.data.data.authorization_url,
          reference,
        };
      } catch (error) {
        this.logger.error('Paystack initialization failed', error);
        throw new BadRequestException('Payment initialization failed');
      }
    }

    // Subscription (flow plan)
    else {
      // Get Paystack plan code from metadata
      const planCode = plan.metadata?.paystack_plan_code as string | undefined;
      if (!planCode) {
        throw new BadRequestException(
          'Plan is not configured for subscriptions',
        );
      }

      const reference = `${plan.slug}-sub-${user.id}-${Date.now()}`;

      try {
        // Create subscription with Paystack
        const response = await firstValueFrom(
          this.httpService.post<IPaymentSubscriptionResponse>(
            `${this.config.baseUrl}/subscription`,
            {
              customer: user.email,
              plan: planCode,
            },
            {
              headers: {
                Authorization: `Bearer ${this.config.secretKey}`,
                'Content-Type': 'application/json',
              },
            },
          ),
        );

        // Save payment record
        const payment = this.paymentsRepo.create({
          transaction_reference: reference,
          amount: Number(plan.price),
          currency: 'NGN',
          status: PaymentStatus.PENDING,
          payment_type: PaymentType.SUBSCRIPTION,
          user: { id: user.id },
          plan: { id: plan.id },
          metadata: {
            subscription_code: response.data.data.subscription_code,
            paystack_plan_code: planCode,
          },
        });

        await this.paymentsRepo.save(payment);

        return {
          subscription_code: response.data.data.subscription_code,
          email_token: response.data.data.email_token,
          reference,
        };
      } catch (error) {
        this.logger.error('Paystack subscription failed', error);
        throw new BadRequestException('Subscription initialization failed');
      }
    }
  }

  /**
   * Verify payment and update user tasks_left
   */
  public async verifyPayment(dto: VerifyPaymentDto) {
    try {
      // Verify transaction with Paystack
      const response = await firstValueFrom(
        this.httpService.get<IPaymentVerificationResponse>(
          `${this.config.baseUrl}/transaction/verify/${dto.reference}`,
          {
            headers: {
              Authorization: `Bearer ${this.config.secretKey}`,
            },
          },
        ),
      );

      if (response.data.data.status !== 'success') {
        throw new BadRequestException('Payment verification failed');
      }

      // Find payment record
      const payment = await this.paymentsRepo.findOne({
        where: { transaction_reference: dto.reference },
        relations: ['user', 'plan'],
      });

      if (!payment) {
        throw new NotFoundException('Payment record not found');
      }

      if (!payment.plan) {
        throw new Error('Payment plan not found');
      }

      // Update payment status
      payment.status = PaymentStatus.SUCCESS;
      payment.metadata = {
        ...payment.metadata,
        verified_at: new Date().toISOString(),
        paystack_response: response.data.data,
      };

      await this.paymentsRepo.save(payment);

      // Update user tasks_left based on payment type
      const user = await this.usersRepo.findOne({
        where: { id: payment.user.id },
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }

      if (payment.payment_type === PaymentType.ONE_TIME) {
        // Focus plan: increment tasks_left by quantity
        const currentTasks = user.tasks_left || 0;
        const additionalTasks = payment.quantity || 1;
        user.tasks_left = currentTasks + additionalTasks;

        // Update user's plan to Focus plan
        user.plan = payment.plan;
      } else if (payment.payment_type === PaymentType.SUBSCRIPTION) {
        // Flow plan: set tasks_left to null (unlimited) and update plan
        user.tasks_left = null;
        user.plan = payment.plan;

        // Set renewal date for subscription
        const renewalDate = new Date();
        renewalDate.setMonth(renewalDate.getMonth() + 1);
        user.renews_at = renewalDate;
      }

      await this.usersRepo.save(user);

      return {
        status: 'success',
        message: 'Payment verified successfully',
        payment,
      };
    } catch (error) {
      this.logger.error('Payment verification failed', error);
      throw new BadRequestException('Payment verification failed');
    }
  }

  /**
   * Handle Paystack webhooks
   */
  public async handleWebhook(
    payload: { event: string; data: { reference: string } },
    signature: string,
  ) {
    // Verify webhook signature
    const hash = crypto
      .createHmac('sha512', this.config.secretKey || '')
      .update(JSON.stringify(payload))
      .digest('hex');

    if (hash !== signature) {
      throw new BadRequestException('Invalid signature');
    }

    const event = payload.event;

    // Handle charge.success event
    if (event === 'charge.success') {
      const reference = payload.data.reference;

      // Auto-verify payment
      await this.verifyPayment({ reference });

      return { status: 'success', message: 'Webhook processed' };
    }

    return { status: 'ignored', message: 'Event not handled' };
  }

  /**
   * Get all payments for current user
   */
  public async findAll(currentUser: IRequestUser) {
    return await this.paymentsRepo.find({
      where: { user: { id: currentUser.id } },
      relations: ['plan'],
      order: { registry: { createdAt: 'DESC' } },
    });
  }

  /**
   * Get payment by ID
   */
  public async findOne(id: string, currentUser: IRequestUser) {
    const payment = await this.paymentsRepo.findOne({
      where: { id, user: { id: currentUser.id } },
      relations: ['plan', 'user'],
    });

    if (!payment) {
      throw new NotFoundException('Payment not found');
    }

    return payment;
  }
}
</file>

<file path="src/main.ts">
import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import cookieParser from 'cookie-parser';
import helmet from 'helmet';

import helmetConfig from 'src/core/config/helmet.config';
import * as pkg from '../package.json';
import { AppModule } from './app.module';
import { ApiConfig } from './core/config/app.config';
import tokensConfig from './core/config/tokens.config';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const logger = new Logger(bootstrap.name);

  const env = ApiConfig.NODE_ENV;
  const port = ApiConfig.API_PORT;
  const versionMajor = pkg.version?.split('.')[0] ?? '1';
  const prefix = `api/v${versionMajor}`;
  const title: string = pkg?.name?.replace(/-/g, ' ').toUpperCase() ?? '';

  app.use(helmet(helmetConfig));
  app.enableCors({
    origin: '*',
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Cookie'],
  });
  app.use(cookieParser());
  app.setGlobalPrefix(prefix);

  const config = new DocumentBuilder()
    .setTitle(title)
    .setDescription(pkg.description)
    .addCookieAuth(tokensConfig.access)
    .setVersion(pkg.version)
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('docs', app, document, {
    useGlobalPrefix: true,
    customSiteTitle: title,
    swaggerOptions: {
      tagsSorter: 'alpha',
    },
  });

  await app.listen(port ?? 3000);
  logger.log(
    `📚 Swagger documentation available at http://localhost:${port}/${prefix}/docs`,
  );
  logger.log(
    `🚀 Application is running on: http://localhost:${port}/${prefix}`,
  );
  logger.log(`🌍 Environment: ${env}`);
}

void bootstrap();
</file>

<file path="Dockerfile">
# Use the official Node.js image as the base image
FROM node:20-alpine AS base

# Set the working directory inside the container
WORKDIR /usr/src/app

# Copy package.json and package-lock.json to the working directory
COPY package*.json ./

# Install the application dependencies
RUN npm install

# Copy the rest of the application files
COPY . .

# Build the NestJS application
RUN npm run build

# Expose the application port
EXPOSE ${API_PORT}

# Command to run the application
CMD ["sh", "-c", "yarn migration:run && node dist/src/main.js"]
</file>

</files>
