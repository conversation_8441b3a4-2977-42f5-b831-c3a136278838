import {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  Logger,
  NestInterceptor,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

@Injectable()
export class Traffic implements NestInterceptor {
  private readonly logger = new Logger(Traffic.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();
    const { method, url } = request;
    const startTime = Date.now();

    return next.handle().pipe(
      tap(() => {
        const endTime = Date.now();
        const timeTaken = endTime - startTime;
        const statusCode = response.statusCode;
        const icon = statusCode < 400 ? '🛈' : '❌';

        this.logger.log(
          `${icon} [${statusCode}] ${method} ${url} ${timeTaken}ms`,
        );
      }),
    );
  }
}
