import {
  IsDateString,
  <PERSON>Enum,
  <PERSON>NotEmpty,
  Is<PERSON><PERSON>al,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';

import { TaskStatus } from '../enums/task-status.enum';

export class CreateTaskDto {
  /**
   * Task title
   * @example "Complete project documentation"
   */
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  title: string;

  /**
   * Detailed description of the task
   * @example "Write comprehensive documentation for the API endpoints"
   */
  @IsString()
  @IsOptional()
  description?: string;

  /**
   * Task status
   * @example "todo"
   */
  @IsEnum(TaskStatus)
  @IsOptional()
  status?: TaskStatus;

  /**
   * Task due date
   * @example "2025-10-15T10:00:00Z"
   */
  @IsDateString()
  @IsOptional()
  due_at?: Date;
}
