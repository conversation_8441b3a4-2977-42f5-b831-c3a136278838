<html lang='en'>
  <head>
    <meta charset='UTF-8' />
    <meta name='viewport' content='width=device-width, initial-scale=1.0' />
    <title>Email Verification</title>
    <style>
      body {
        font-family:
          -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
          'Helvetica Neue', Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
      }
      .container {
        background-color: #ffffff;
        border-radius: 8px;
        padding: 40px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .header {
        text-align: center;
        margin-bottom: 30px;
      }
      .header h1 {
        color: #2563eb;
        margin: 0;
      }
      .code-container {
        background-color: #f3f4f6;
        border-radius: 8px;
        padding: 30px;
        text-align: center;
        margin: 30px 0;
      }
      .code {
        font-size: 32px;
        font-weight: bold;
        letter-spacing: 8px;
        color: #1f2937;
        margin: 0;
      }
      .footer {
        text-align: center;
        margin-top: 30px;
        color: #6b7280;
        font-size: 14px;
      }
      .warning {
        background-color: #fef3c7;
        border-left: 4px solid #f59e0b;
        padding: 12px;
        margin-top: 20px;
        border-radius: 4px;
      }
      .verify-button {
        display: inline-block;
        background-color: #2563eb;
        color: white;
        padding: 12px 24px;
        text-decoration: none;
        border-radius: 6px;
        font-weight: bold;
        margin: 20px 0;
        border: none;
        cursor: pointer;
        font-size: 16px;
      }
      .verify-button:hover {
        background-color: #1d4ed8;
      }
      .endpoint-info {
        background-color: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        padding: 16px;
        margin: 20px 0;
        font-family: 'Courier New', monospace;
        font-size: 14px;
      }
      .endpoint-info strong {
        color: #1f2937;
      }
    </style>
  </head>
  <body>
    <div class='container'>
      <div class='header'>
        <h1>Email Verification</h1>
      </div>

      <p>Hello{{#if name}} {{name}}{{/if}},</p>

      <p>
        Thank you for signing up with Aiki! To complete your registration,
        please use the verification code below:
      </p>

      <div class='code-container'>
        <p class='code'>{{code}}</p>
      </div>

      <p>
        This verification code will expire in
        <strong>10 minutes</strong>.
      </p>

      <div class='endpoint-info'>
        <p style='margin: 0 0 10px 0;'><strong>API Endpoint:</strong></p>
        <p style='margin: 0 0 10px 0;'>POST /api/v1/auth/verify-email</p>
        <p style='margin: 0;'><strong>Payload:</strong>
          {"email": "your-email", "code": "{{code}}"}</p>
      </div>

      <div style='text-align: center;'>
        <p><strong>Use this verification code with the API endpoint above, or
            copy the code and use your preferred API client.</strong></p>
        <p style='font-size: 14px; color: #6b7280;'>
          After verification, you can sign in at:
          <strong>/api/v1/auth/signin</strong>
        </p>
      </div>

      <div class='warning'>
        <p style='margin: 0;'>
          <strong>Security Notice:</strong>
          If you didn't request this verification code, please ignore this
          email.
        </p>
      </div>

      <div class='footer'>
        <p>Best regards,</p>
        <strong>The Aiki Team</strong>
        <p style='font-size: 12px; color: #9ca3af;'>
          This is an automated email, please do not reply.
        </p>
      </div>
    </div>
  </body>
</html>