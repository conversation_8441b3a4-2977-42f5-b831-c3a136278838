# General
NODE_ENV=production
API_PORT=5155
API_TOKEN="API_CONFIG"
TZ=Africa/Lagos
API_URL="http://aiki-api:5155"

PUID=1000
PGID=1000

# Database
DB_URL=pg:5432/aiki
REDIS_HOST=redis
REDIS_PORT=6379

# NOTE: All TTLs are in seconds
# JWT
JWT_SECRET="64-character-secret-key"
JWT_ISSUER="http://aiki-api:5155"
JWT_AUDIENCE="http://aiki-api:5155"
JWT_ACCESS_TOKEN_TTL=900 # 15 minutes
JWT_REFRESH_TOKEN_TTL=604800 # 7 days

# THROTTLER
THROTTLER_TTL=60
THROTTLER_LIMIT=10

# HTTP (axios)
HTTP_TIMEOUT=5000
HTTP_MAX_REDIRECTS=5

# Email
VERIFICATION_CODE_TTL=600 # 10 minutes (for account verification)
EMAIL_FROM='Aiki Team'
EMAIL_SENDER=''
EMAIL_HOST="smtp.zoho.com"
EMAIL_PORT=465
EMAIL_USERNAME=""
EMAIL_PASSWORD=""
EMAIL_SECURE=true

# Paystack
PAYSTACK_SECRET_KEY=""
PAYSTACK_PUBLIC_KEY=""
PAYSTACK_BASE_URL="https://api.paystack.co"
PAYSTACK_CALLBACK_URL="https://aiki.dovely.tech/api/v1/payments/callback"
PAYSTACK_WEBHOOK_URL="https://aiki.dovely.tech/api/v1/payments/webhook"
FLOW_PLAN_CODE=""


